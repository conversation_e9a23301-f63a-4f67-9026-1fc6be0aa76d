{"ast": null, "code": "import { MediaMatcher } from '@angular/cdk/layout';\nimport { InjectionToken, inject, ANIMATION_MODULE_TYPE } from '@angular/core';\n\n/** Injection token used to configure the animations in Angular Material. */\nconst MATERIAL_ANIMATIONS = /*#__PURE__*/new InjectionToken('MATERIAL_ANIMATIONS');\n/**\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n * @docs-private\n */\nlet AnimationCurves = /*#__PURE__*/(() => {\n  class AnimationCurves {\n    static STANDARD_CURVE = 'cubic-bezier(0.4,0.0,0.2,1)';\n    static DECELERATION_CURVE = 'cubic-bezier(0.0,0.0,0.2,1)';\n    static ACCELERATION_CURVE = 'cubic-bezier(0.4,0.0,1,1)';\n    static SHARP_CURVE = 'cubic-bezier(0.4,0.0,0.6,1)';\n  }\n  return AnimationCurves;\n})();\n/**\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n * @docs-private\n */\nlet AnimationDurations = /*#__PURE__*/(() => {\n  class AnimationDurations {\n    static COMPLEX = '375ms';\n    static ENTERING = '225ms';\n    static EXITING = '195ms';\n  }\n  return AnimationDurations;\n})();\n/**\n * Returns whether animations have been disabled by DI. Must be called in a DI context.\n * @docs-private\n */\nfunction _animationsDisabled() {\n  if (inject(MATERIAL_ANIMATIONS, {\n    optional: true\n  })?.animationsDisabled || inject(ANIMATION_MODULE_TYPE, {\n    optional: true\n  }) === 'NoopAnimations') {\n    return true;\n  }\n  const mediaMatcher = inject(MediaMatcher);\n  return mediaMatcher.matchMedia('(prefers-reduced-motion)').matches;\n}\nexport { AnimationCurves as A, MATERIAL_ANIMATIONS as M, _animationsDisabled as _, AnimationDurations as a };\n//# sourceMappingURL=animation-DfMFjxHu.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}