{"ast": null, "code": "import { HttpEventType } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, finalize, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../services/loading.service\";\nimport * as i4 from \"../services/rate-limit.service\";\nexport class AuthInterceptor {\n  constructor(authService, router, loadingService, rateLimitService) {\n    this.authService = authService;\n    this.router = router;\n    this.loadingService = loadingService;\n    this.rateLimitService = rateLimitService;\n  }\n  intercept(request, next) {\n    // Show loading indicator\n    this.loadingService.show();\n    // Add auth token if available\n    const token = this.authService.getToken();\n    if (token && !this.authService.isTokenExpired()) {\n      request = request.clone({\n        setHeaders: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json',\n          'X-Requested-With': 'XMLHttpRequest'\n        }\n      });\n    }\n    // Add CSRF protection\n    request = request.clone({\n      setHeaders: {\n        'X-CSRF-Token': this.generateCSRFToken()\n      }\n    });\n    return next.handle(request).pipe(tap(event => {\n      // Handle successful responses and extract rate limit headers\n      if (event.type === HttpEventType.Response) {\n        const response = event;\n        if (response.headers) {\n          this.rateLimitService.handleRateLimitResponse(this.extractHeaders(response.headers));\n        }\n      }\n    }), catchError(error => {\n      this.handleError(error);\n      return throwError(() => error);\n    }), finalize(() => {\n      // Hide loading indicator\n      this.loadingService.hide();\n    }));\n  }\n  handleError(error) {\n    console.log('🚨 HTTP Error intercepted:', error);\n    console.log('🔍 Error details:', {\n      status: error.status,\n      message: error.message,\n      url: error.url,\n      statusText: error.statusText,\n      error: error.error\n    });\n    // Enhanced rate limit detection for all error types (must be first)\n    if (this.detectRateLimitError(error)) {\n      console.warn('🚦 Rate limit detected via enhanced analysis - triggering popup');\n      this.handleRateLimitError(error);\n      return; // Don't continue with other error handling\n    } else {\n      console.log('❌ Rate limit NOT detected - continuing with normal error handling');\n    }\n    switch (error.status) {\n      case 401:\n        // Check if this is an account lockout vs. token expiry\n        const errorMessage = error.error?.error?.message || error.error?.message || '';\n        if (errorMessage.includes('temporarily locked') || errorMessage.includes('multiple failed login attempts')) {\n          // This is an account lockout - don't auto-logout, let the login component handle it\n          console.log('Account lockout detected - allowing login component to handle');\n          return;\n        }\n        // Regular unauthorized - token expired or invalid\n        this.authService.logout();\n        this.router.navigate(['/auth/login'], {\n          queryParams: {\n            message: 'Session expired. Please login again.'\n          }\n        });\n        break;\n      case 403:\n        // Forbidden - insufficient permissions\n        this.router.navigate(['/unauthorized']);\n        break;\n      case 429:\n        // Too many requests - should already be handled by enhanced detection above\n        console.warn('🚦 Direct 429 status - already handled by enhanced detection');\n        break;\n      case 0:\n        // Network error - could be rate limit with CORS issues (handled by enhanced detection)\n        console.error('🌐 Network error. Please check your connection.');\n        break;\n      default:\n        console.error('❌ HTTP Error:', error);\n    }\n  }\n  /**\n   * Enhanced rate limit detection from various error patterns\n   * Now specifically searches for \"rate\" in error messages to avoid false positives\n   */\n  detectRateLimitError(error) {\n    // Direct 429 status\n    if (error.status === 429) return true;\n    // Check error message for rate limit indicators\n    const errorMessage = error.message?.toLowerCase() || '';\n    const errorText = error.error?.toString?.()?.toLowerCase() || '';\n    const errorDetails = error.error?.error?.message?.toLowerCase() || '';\n    const statusText = error.statusText?.toLowerCase() || '';\n    const url = error.url?.toLowerCase() || '';\n    // More specific rate limit indicators - must contain \"rate\" or very specific rate limit terms\n    const rateLimitIndicators = ['too many requests', '(too many requests)',\n    // Your specific console error format\n    'rate limit', 'ratelimit', 'rate-limit', 'rate exceeded', 'quota exceeded', 'throttled', 'throttle'];\n    // Check if error contains specific rate limit indicators\n    const hasRateLimitPattern = rateLimitIndicators.some(indicator => errorMessage.includes(indicator) || errorText.includes(indicator) || errorDetails.includes(indicator) || statusText.includes(indicator));\n    // Only check for specific rate-related network failures\n    // Must have \"rate\" in the error message to be considered a rate limit\n    const hasRateInMessage = errorMessage.includes('rate') || errorText.includes('rate') || errorDetails.includes('rate') || statusText.includes('rate');\n    // Network failure that specifically mentions rate limiting\n    const hasNetworkFailureWithRate = (error.status === 0 || errorMessage.includes('net::err_failed')) && hasRateInMessage;\n    // Log detection for debugging\n    if (hasRateLimitPattern || hasNetworkFailureWithRate) {\n      console.log('🚦 Rate limit detected:', {\n        status: error.status,\n        message: errorMessage,\n        text: errorText,\n        details: errorDetails,\n        statusText: statusText,\n        url: url,\n        rateLimitPattern: hasRateLimitPattern,\n        networkFailureWithRate: hasNetworkFailureWithRate,\n        hasRateInMessage: hasRateInMessage\n      });\n      return true;\n    }\n    console.log('❌ Rate limit NOT detected - error does not contain rate limit indicators:', {\n      status: error.status,\n      message: errorMessage.substring(0, 100) + '...',\n      hasRateInMessage: hasRateInMessage\n    });\n    return false;\n  }\n  /**\n   * Handle rate limit error with smart fallback data\n   */\n  handleRateLimitError(error) {\n    console.log('🚦 Handling rate limit error:', error);\n    // Try to extract headers if available\n    let headers = {};\n    if (error.headers) {\n      headers = this.extractHeaders(error.headers);\n    }\n    // Check if we have actual rate limit headers from the server\n    const hasRateLimitHeaders = headers['retry-after'] || headers['x-ratelimit-retryafter'] || headers['x-ratelimit-limit'] || headers['x-ratelimit-remaining'];\n    // If no rate limit headers but we detected a rate limit, provide smart fallback values\n    if (!hasRateLimitHeaders) {\n      headers = this.generateSmartRateLimitHeaders(error.url || '');\n      console.log('🔄 Using smart fallback rate limit headers:', headers);\n    } else {\n      console.log('✅ Using actual rate limit headers from server:', headers);\n    }\n    // Handle the rate limit response\n    this.rateLimitService.handleRateLimitResponse(headers, error);\n  }\n  /**\n   * Generate smart fallback rate limit headers based on URL patterns\n   */\n  generateSmartRateLimitHeaders(url) {\n    const now = Math.floor(Date.now() / 1000);\n    // Match backend configuration: RATE_LIMIT_WINDOW=1 minute, RATE_LIMIT_MAX=2\n    let retryAfter = 60; // 1 minute to match backend\n    let limit = 2; // Match backend RATE_LIMIT_MAX=2\n    if (url.includes('/auth/login')) {\n      retryAfter = 60; // 1 minute for login attempts (match backend)\n      limit = 2;\n    } else if (url.includes('/auth/register')) {\n      retryAfter = 60; // 1 minute for registration\n      limit = 2;\n    } else if (url.includes('/auth/reset') || url.includes('/auth/forgot')) {\n      retryAfter = 60; // 1 minute for password reset\n      limit = 2;\n    } else if (url.includes('/auth/verify')) {\n      retryAfter = 60; // 1 minute for verification codes\n      limit = 2;\n    } else if (url.includes('/auth/')) {\n      retryAfter = 60; // 1 minute for other auth endpoints\n      limit = 2;\n    }\n    return {\n      'retry-after': retryAfter.toString(),\n      'x-ratelimit-limit': limit.toString(),\n      'x-ratelimit-remaining': '0',\n      'x-ratelimit-reset': (now + retryAfter).toString(),\n      'x-ratelimit-totalrequests': limit.toString()\n    };\n  }\n  generateCSRFToken() {\n    // Simple CSRF token generation\n    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n  }\n  /**\n   * Extract headers from HttpHeaders object\n   */\n  extractHeaders(httpHeaders) {\n    const headers = {};\n    if (httpHeaders && httpHeaders.keys) {\n      httpHeaders.keys().forEach(key => {\n        headers[key.toLowerCase()] = httpHeaders.get(key);\n      });\n    }\n    return headers;\n  }\n  static #_ = this.ɵfac = function AuthInterceptor_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || AuthInterceptor)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.LoadingService), i0.ɵɵinject(i4.RateLimitService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthInterceptor,\n    factory: AuthInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["HttpEventType", "throwError", "catchError", "finalize", "tap", "AuthInterceptor", "constructor", "authService", "router", "loadingService", "rateLimitService", "intercept", "request", "next", "show", "token", "getToken", "isTokenExpired", "clone", "setHeaders", "Authorization", "generateCSRFToken", "handle", "pipe", "event", "type", "Response", "response", "headers", "handleRateLimitResponse", "extractHeaders", "error", "handleError", "hide", "console", "log", "status", "message", "url", "statusText", "detectRateLimitError", "warn", "handleRateLimitError", "errorMessage", "includes", "logout", "navigate", "queryParams", "toLowerCase", "errorText", "toString", "errorDetails", "rateLimitIndicators", "hasRateLimitPattern", "some", "indicator", "hasRateInMessage", "hasNetworkFailureWithRate", "text", "details", "rateLimitPattern", "networkFailureWithRate", "substring", "hasRateLimitHeaders", "generateSmartRateLimitHeaders", "now", "Math", "floor", "Date", "retryAfter", "limit", "random", "httpHeaders", "keys", "for<PERSON>ach", "key", "get", "_", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "i3", "LoadingService", "i4", "RateLimitService", "_2", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Downloads\\study\\apps\\ai\\cody\\Modular backend secure user system and payment_Cody\\frontend\\src\\app\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse, HttpResponse, HttpEventType } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { catchError, finalize, tap } from 'rxjs/operators';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../services/auth.service';\nimport { LoadingService } from '../services/loading.service';\nimport { RateLimitService } from '../services/rate-limit.service';\n\n@Injectable()\nexport class AuthInterceptor implements HttpInterceptor {\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private loadingService: LoadingService,\n    private rateLimitService: RateLimitService\n  ) {}\n\n  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\n    // Show loading indicator\n    this.loadingService.show();\n\n    // Add auth token if available\n    const token = this.authService.getToken();\n    if (token && !this.authService.isTokenExpired()) {\n      request = request.clone({\n        setHeaders: {\n          Authorization: `Bearer ${token}`,\n          'Content-Type': 'application/json',\n          'X-Requested-With': 'XMLHttpRequest'\n        }\n      });\n    }\n\n    // Add CSRF protection\n    request = request.clone({\n      setHeaders: {\n        'X-CSRF-Token': this.generateCSRFToken()\n      }\n    });\n\n    return next.handle(request).pipe(\n      tap((event: HttpEvent<any>) => {\n        // Handle successful responses and extract rate limit headers\n        if (event.type === HttpEventType.Response) {\n          const response = event as HttpResponse<any>;\n          if (response.headers) {\n            this.rateLimitService.handleRateLimitResponse(this.extractHeaders(response.headers));\n          }\n        }\n      }),\n      catchError((error: HttpErrorResponse) => {\n        this.handleError(error);\n        return throwError(() => error);\n      }),\n      finalize(() => {\n        // Hide loading indicator\n        this.loadingService.hide();\n      })\n    );\n  }\n\n  private handleError(error: HttpErrorResponse): void {\n    console.log('🚨 HTTP Error intercepted:', error);\n    console.log('🔍 Error details:', {\n      status: error.status,\n      message: error.message,\n      url: error.url,\n      statusText: error.statusText,\n      error: error.error\n    });\n    \n    // Enhanced rate limit detection for all error types (must be first)\n    if (this.detectRateLimitError(error)) {\n      console.warn('🚦 Rate limit detected via enhanced analysis - triggering popup');\n      this.handleRateLimitError(error);\n      return; // Don't continue with other error handling\n    } else {\n      console.log('❌ Rate limit NOT detected - continuing with normal error handling');\n    }\n\n    switch (error.status) {\n      case 401:\n        // Check if this is an account lockout vs. token expiry\n        const errorMessage = error.error?.error?.message || error.error?.message || '';\n        \n        if (errorMessage.includes('temporarily locked') || \n            errorMessage.includes('multiple failed login attempts')) {\n          // This is an account lockout - don't auto-logout, let the login component handle it\n          console.log('Account lockout detected - allowing login component to handle');\n          return;\n        }\n        \n        // Regular unauthorized - token expired or invalid\n        this.authService.logout();\n        this.router.navigate(['/auth/login'], {\n          queryParams: { message: 'Session expired. Please login again.' }\n        });\n        break;\n      case 403:\n        // Forbidden - insufficient permissions\n        this.router.navigate(['/unauthorized']);\n        break;\n      case 429:\n        // Too many requests - should already be handled by enhanced detection above\n        console.warn('🚦 Direct 429 status - already handled by enhanced detection');\n        break;\n      case 0:\n        // Network error - could be rate limit with CORS issues (handled by enhanced detection)\n        console.error('🌐 Network error. Please check your connection.');\n        break;\n      default:\n        console.error('❌ HTTP Error:', error);\n    }\n  }\n\n  /**\n   * Enhanced rate limit detection from various error patterns\n   * Now specifically searches for \"rate\" in error messages to avoid false positives\n   */\n  private detectRateLimitError(error: HttpErrorResponse): boolean {\n    // Direct 429 status\n    if (error.status === 429) return true;\n    \n    // Check error message for rate limit indicators\n    const errorMessage = error.message?.toLowerCase() || '';\n    const errorText = error.error?.toString?.()?.toLowerCase() || '';\n    const errorDetails = error.error?.error?.message?.toLowerCase() || '';\n    const statusText = error.statusText?.toLowerCase() || '';\n    const url = error.url?.toLowerCase() || '';\n    \n    // More specific rate limit indicators - must contain \"rate\" or very specific rate limit terms\n    const rateLimitIndicators = [\n      'too many requests',\n      '(too many requests)', // Your specific console error format\n      'rate limit',\n      'ratelimit',\n      'rate-limit',\n      'rate exceeded',\n      'quota exceeded',\n      'throttled',\n      'throttle'\n    ];\n    \n    // Check if error contains specific rate limit indicators\n    const hasRateLimitPattern = rateLimitIndicators.some(indicator => \n      errorMessage.includes(indicator) || \n      errorText.includes(indicator) || \n      errorDetails.includes(indicator) ||\n      statusText.includes(indicator)\n    );\n    \n    // Only check for specific rate-related network failures\n    // Must have \"rate\" in the error message to be considered a rate limit\n    const hasRateInMessage = errorMessage.includes('rate') || \n                            errorText.includes('rate') || \n                            errorDetails.includes('rate') ||\n                            statusText.includes('rate');\n    \n    // Network failure that specifically mentions rate limiting\n    const hasNetworkFailureWithRate = (error.status === 0 || errorMessage.includes('net::err_failed')) && hasRateInMessage;\n    \n    // Log detection for debugging\n    if (hasRateLimitPattern || hasNetworkFailureWithRate) {\n      console.log('🚦 Rate limit detected:', {\n        status: error.status,\n        message: errorMessage,\n        text: errorText,\n        details: errorDetails,\n        statusText: statusText,\n        url: url,\n        rateLimitPattern: hasRateLimitPattern,\n        networkFailureWithRate: hasNetworkFailureWithRate,\n        hasRateInMessage: hasRateInMessage\n      });\n      return true;\n    }\n    \n    console.log('❌ Rate limit NOT detected - error does not contain rate limit indicators:', {\n      status: error.status,\n      message: errorMessage.substring(0, 100) + '...',\n      hasRateInMessage: hasRateInMessage\n    });\n    \n    return false;\n  }\n\n  /**\n   * Handle rate limit error with smart fallback data\n   */\n  private handleRateLimitError(error: HttpErrorResponse): void {\n    console.log('🚦 Handling rate limit error:', error);\n    \n    // Try to extract headers if available\n    let headers: {[key: string]: string} = {};\n    \n    if (error.headers) {\n      headers = this.extractHeaders(error.headers);\n    }\n    \n    // Check if we have actual rate limit headers from the server\n    const hasRateLimitHeaders = headers['retry-after'] || \n                                headers['x-ratelimit-retryafter'] || \n                                headers['x-ratelimit-limit'] ||\n                                headers['x-ratelimit-remaining'];\n    \n    // If no rate limit headers but we detected a rate limit, provide smart fallback values\n    if (!hasRateLimitHeaders) {\n      headers = this.generateSmartRateLimitHeaders(error.url || '');\n      console.log('🔄 Using smart fallback rate limit headers:', headers);\n    } else {\n      console.log('✅ Using actual rate limit headers from server:', headers);\n    }\n    \n    // Handle the rate limit response\n    this.rateLimitService.handleRateLimitResponse(headers, error);\n  }\n\n  /**\n   * Generate smart fallback rate limit headers based on URL patterns\n   */\n  private generateSmartRateLimitHeaders(url: string): {[key: string]: string} {\n    const now = Math.floor(Date.now() / 1000);\n    \n    // Match backend configuration: RATE_LIMIT_WINDOW=1 minute, RATE_LIMIT_MAX=2\n    let retryAfter = 60; // 1 minute to match backend\n    let limit = 2; // Match backend RATE_LIMIT_MAX=2\n    \n    if (url.includes('/auth/login')) {\n      retryAfter = 60; // 1 minute for login attempts (match backend)\n      limit = 2;\n    } else if (url.includes('/auth/register')) {\n      retryAfter = 60; // 1 minute for registration\n      limit = 2;\n    } else if (url.includes('/auth/reset') || url.includes('/auth/forgot')) {\n      retryAfter = 60; // 1 minute for password reset\n      limit = 2;\n    } else if (url.includes('/auth/verify')) {\n      retryAfter = 60; // 1 minute for verification codes\n      limit = 2;\n    } else if (url.includes('/auth/')) {\n      retryAfter = 60; // 1 minute for other auth endpoints\n      limit = 2;\n    }\n    \n    return {\n      'retry-after': retryAfter.toString(),\n      'x-ratelimit-limit': limit.toString(),\n      'x-ratelimit-remaining': '0',\n      'x-ratelimit-reset': (now + retryAfter).toString(),\n      'x-ratelimit-totalrequests': limit.toString()\n    };\n  }\n\n  private generateCSRFToken(): string {\n    // Simple CSRF token generation\n    return Math.random().toString(36).substring(2, 15) + \n           Math.random().toString(36).substring(2, 15);\n  }\n\n  /**\n   * Extract headers from HttpHeaders object\n   */\n  private extractHeaders(httpHeaders: any): {[key: string]: string} {\n    const headers: {[key: string]: string} = {};\n    \n    if (httpHeaders && httpHeaders.keys) {\n      httpHeaders.keys().forEach((key: string) => {\n        headers[key.toLowerCase()] = httpHeaders.get(key);\n      });\n    }\n    \n    return headers;\n  }\n}\n"], "mappings": "AACA,SAAgGA,aAAa,QAAQ,sBAAsB;AAC3I,SAAqBC,UAAU,QAAQ,MAAM;AAC7C,SAASC,UAAU,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,gBAAgB;;;;;;AAO1D,OAAM,MAAOC,eAAe;EAC1BC,YACUC,WAAwB,EACxBC,MAAc,EACdC,cAA8B,EAC9BC,gBAAkC;IAHlC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,gBAAgB,GAAhBA,gBAAgB;EACvB;EAEHC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IACpD;IACA,IAAI,CAACJ,cAAc,CAACK,IAAI,EAAE;IAE1B;IACA,MAAMC,KAAK,GAAG,IAAI,CAACR,WAAW,CAACS,QAAQ,EAAE;IACzC,IAAID,KAAK,IAAI,CAAC,IAAI,CAACR,WAAW,CAACU,cAAc,EAAE,EAAE;MAC/CL,OAAO,GAAGA,OAAO,CAACM,KAAK,CAAC;QACtBC,UAAU,EAAE;UACVC,aAAa,EAAE,UAAUL,KAAK,EAAE;UAChC,cAAc,EAAE,kBAAkB;UAClC,kBAAkB,EAAE;;OAEvB,CAAC;IACJ;IAEA;IACAH,OAAO,GAAGA,OAAO,CAACM,KAAK,CAAC;MACtBC,UAAU,EAAE;QACV,cAAc,EAAE,IAAI,CAACE,iBAAiB;;KAEzC,CAAC;IAEF,OAAOR,IAAI,CAACS,MAAM,CAACV,OAAO,CAAC,CAACW,IAAI,CAC9BnB,GAAG,CAAEoB,KAAqB,IAAI;MAC5B;MACA,IAAIA,KAAK,CAACC,IAAI,KAAKzB,aAAa,CAAC0B,QAAQ,EAAE;QACzC,MAAMC,QAAQ,GAAGH,KAA0B;QAC3C,IAAIG,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAAClB,gBAAgB,CAACmB,uBAAuB,CAAC,IAAI,CAACC,cAAc,CAACH,QAAQ,CAACC,OAAO,CAAC,CAAC;QACtF;MACF;IACF,CAAC,CAAC,EACF1B,UAAU,CAAE6B,KAAwB,IAAI;MACtC,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;MACvB,OAAO9B,UAAU,CAAC,MAAM8B,KAAK,CAAC;IAChC,CAAC,CAAC,EACF5B,QAAQ,CAAC,MAAK;MACZ;MACA,IAAI,CAACM,cAAc,CAACwB,IAAI,EAAE;IAC5B,CAAC,CAAC,CACH;EACH;EAEQD,WAAWA,CAACD,KAAwB;IAC1CG,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEJ,KAAK,CAAC;IAChDG,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;MAC/BC,MAAM,EAAEL,KAAK,CAACK,MAAM;MACpBC,OAAO,EAAEN,KAAK,CAACM,OAAO;MACtBC,GAAG,EAAEP,KAAK,CAACO,GAAG;MACdC,UAAU,EAAER,KAAK,CAACQ,UAAU;MAC5BR,KAAK,EAAEA,KAAK,CAACA;KACd,CAAC;IAEF;IACA,IAAI,IAAI,CAACS,oBAAoB,CAACT,KAAK,CAAC,EAAE;MACpCG,OAAO,CAACO,IAAI,CAAC,iEAAiE,CAAC;MAC/E,IAAI,CAACC,oBAAoB,CAACX,KAAK,CAAC;MAChC,OAAO,CAAC;IACV,CAAC,MAAM;MACLG,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC;IAClF;IAEA,QAAQJ,KAAK,CAACK,MAAM;MAClB,KAAK,GAAG;QACN;QACA,MAAMO,YAAY,GAAGZ,KAAK,CAACA,KAAK,EAAEA,KAAK,EAAEM,OAAO,IAAIN,KAAK,CAACA,KAAK,EAAEM,OAAO,IAAI,EAAE;QAE9E,IAAIM,YAAY,CAACC,QAAQ,CAAC,oBAAoB,CAAC,IAC3CD,YAAY,CAACC,QAAQ,CAAC,gCAAgC,CAAC,EAAE;UAC3D;UACAV,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;UAC5E;QACF;QAEA;QACA,IAAI,CAAC5B,WAAW,CAACsC,MAAM,EAAE;QACzB,IAAI,CAACrC,MAAM,CAACsC,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;UACpCC,WAAW,EAAE;YAAEV,OAAO,EAAE;UAAsC;SAC/D,CAAC;QACF;MACF,KAAK,GAAG;QACN;QACA,IAAI,CAAC7B,MAAM,CAACsC,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;QACvC;MACF,KAAK,GAAG;QACN;QACAZ,OAAO,CAACO,IAAI,CAAC,8DAA8D,CAAC;QAC5E;MACF,KAAK,CAAC;QACJ;QACAP,OAAO,CAACH,KAAK,CAAC,iDAAiD,CAAC;QAChE;MACF;QACEG,OAAO,CAACH,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACzC;EACF;EAEA;;;;EAIQS,oBAAoBA,CAACT,KAAwB;IACnD;IACA,IAAIA,KAAK,CAACK,MAAM,KAAK,GAAG,EAAE,OAAO,IAAI;IAErC;IACA,MAAMO,YAAY,GAAGZ,KAAK,CAACM,OAAO,EAAEW,WAAW,EAAE,IAAI,EAAE;IACvD,MAAMC,SAAS,GAAGlB,KAAK,CAACA,KAAK,EAAEmB,QAAQ,GAAE,CAAE,EAAEF,WAAW,EAAE,IAAI,EAAE;IAChE,MAAMG,YAAY,GAAGpB,KAAK,CAACA,KAAK,EAAEA,KAAK,EAAEM,OAAO,EAAEW,WAAW,EAAE,IAAI,EAAE;IACrE,MAAMT,UAAU,GAAGR,KAAK,CAACQ,UAAU,EAAES,WAAW,EAAE,IAAI,EAAE;IACxD,MAAMV,GAAG,GAAGP,KAAK,CAACO,GAAG,EAAEU,WAAW,EAAE,IAAI,EAAE;IAE1C;IACA,MAAMI,mBAAmB,GAAG,CAC1B,mBAAmB,EACnB,qBAAqB;IAAE;IACvB,YAAY,EACZ,WAAW,EACX,YAAY,EACZ,eAAe,EACf,gBAAgB,EAChB,WAAW,EACX,UAAU,CACX;IAED;IACA,MAAMC,mBAAmB,GAAGD,mBAAmB,CAACE,IAAI,CAACC,SAAS,IAC5DZ,YAAY,CAACC,QAAQ,CAACW,SAAS,CAAC,IAChCN,SAAS,CAACL,QAAQ,CAACW,SAAS,CAAC,IAC7BJ,YAAY,CAACP,QAAQ,CAACW,SAAS,CAAC,IAChChB,UAAU,CAACK,QAAQ,CAACW,SAAS,CAAC,CAC/B;IAED;IACA;IACA,MAAMC,gBAAgB,GAAGb,YAAY,CAACC,QAAQ,CAAC,MAAM,CAAC,IAC9BK,SAAS,CAACL,QAAQ,CAAC,MAAM,CAAC,IAC1BO,YAAY,CAACP,QAAQ,CAAC,MAAM,CAAC,IAC7BL,UAAU,CAACK,QAAQ,CAAC,MAAM,CAAC;IAEnD;IACA,MAAMa,yBAAyB,GAAG,CAAC1B,KAAK,CAACK,MAAM,KAAK,CAAC,IAAIO,YAAY,CAACC,QAAQ,CAAC,iBAAiB,CAAC,KAAKY,gBAAgB;IAEtH;IACA,IAAIH,mBAAmB,IAAII,yBAAyB,EAAE;MACpDvB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE;QACrCC,MAAM,EAAEL,KAAK,CAACK,MAAM;QACpBC,OAAO,EAAEM,YAAY;QACrBe,IAAI,EAAET,SAAS;QACfU,OAAO,EAAER,YAAY;QACrBZ,UAAU,EAAEA,UAAU;QACtBD,GAAG,EAAEA,GAAG;QACRsB,gBAAgB,EAAEP,mBAAmB;QACrCQ,sBAAsB,EAAEJ,yBAAyB;QACjDD,gBAAgB,EAAEA;OACnB,CAAC;MACF,OAAO,IAAI;IACb;IAEAtB,OAAO,CAACC,GAAG,CAAC,2EAA2E,EAAE;MACvFC,MAAM,EAAEL,KAAK,CAACK,MAAM;MACpBC,OAAO,EAAEM,YAAY,CAACmB,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;MAC/CN,gBAAgB,EAAEA;KACnB,CAAC;IAEF,OAAO,KAAK;EACd;EAEA;;;EAGQd,oBAAoBA,CAACX,KAAwB;IACnDG,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEJ,KAAK,CAAC;IAEnD;IACA,IAAIH,OAAO,GAA4B,EAAE;IAEzC,IAAIG,KAAK,CAACH,OAAO,EAAE;MACjBA,OAAO,GAAG,IAAI,CAACE,cAAc,CAACC,KAAK,CAACH,OAAO,CAAC;IAC9C;IAEA;IACA,MAAMmC,mBAAmB,GAAGnC,OAAO,CAAC,aAAa,CAAC,IACtBA,OAAO,CAAC,wBAAwB,CAAC,IACjCA,OAAO,CAAC,mBAAmB,CAAC,IAC5BA,OAAO,CAAC,uBAAuB,CAAC;IAE5D;IACA,IAAI,CAACmC,mBAAmB,EAAE;MACxBnC,OAAO,GAAG,IAAI,CAACoC,6BAA6B,CAACjC,KAAK,CAACO,GAAG,IAAI,EAAE,CAAC;MAC7DJ,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEP,OAAO,CAAC;IACrE,CAAC,MAAM;MACLM,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEP,OAAO,CAAC;IACxE;IAEA;IACA,IAAI,CAAClB,gBAAgB,CAACmB,uBAAuB,CAACD,OAAO,EAAEG,KAAK,CAAC;EAC/D;EAEA;;;EAGQiC,6BAA6BA,CAAC1B,GAAW;IAC/C,MAAM2B,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACH,GAAG,EAAE,GAAG,IAAI,CAAC;IAEzC;IACA,IAAII,UAAU,GAAG,EAAE,CAAC,CAAC;IACrB,IAAIC,KAAK,GAAG,CAAC,CAAC,CAAC;IAEf,IAAIhC,GAAG,CAACM,QAAQ,CAAC,aAAa,CAAC,EAAE;MAC/ByB,UAAU,GAAG,EAAE,CAAC,CAAC;MACjBC,KAAK,GAAG,CAAC;IACX,CAAC,MAAM,IAAIhC,GAAG,CAACM,QAAQ,CAAC,gBAAgB,CAAC,EAAE;MACzCyB,UAAU,GAAG,EAAE,CAAC,CAAC;MACjBC,KAAK,GAAG,CAAC;IACX,CAAC,MAAM,IAAIhC,GAAG,CAACM,QAAQ,CAAC,aAAa,CAAC,IAAIN,GAAG,CAACM,QAAQ,CAAC,cAAc,CAAC,EAAE;MACtEyB,UAAU,GAAG,EAAE,CAAC,CAAC;MACjBC,KAAK,GAAG,CAAC;IACX,CAAC,MAAM,IAAIhC,GAAG,CAACM,QAAQ,CAAC,cAAc,CAAC,EAAE;MACvCyB,UAAU,GAAG,EAAE,CAAC,CAAC;MACjBC,KAAK,GAAG,CAAC;IACX,CAAC,MAAM,IAAIhC,GAAG,CAACM,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACjCyB,UAAU,GAAG,EAAE,CAAC,CAAC;MACjBC,KAAK,GAAG,CAAC;IACX;IAEA,OAAO;MACL,aAAa,EAAED,UAAU,CAACnB,QAAQ,EAAE;MACpC,mBAAmB,EAAEoB,KAAK,CAACpB,QAAQ,EAAE;MACrC,uBAAuB,EAAE,GAAG;MAC5B,mBAAmB,EAAE,CAACe,GAAG,GAAGI,UAAU,EAAEnB,QAAQ,EAAE;MAClD,2BAA2B,EAAEoB,KAAK,CAACpB,QAAQ;KAC5C;EACH;EAEQ7B,iBAAiBA,CAAA;IACvB;IACA,OAAO6C,IAAI,CAACK,MAAM,EAAE,CAACrB,QAAQ,CAAC,EAAE,CAAC,CAACY,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAC3CI,IAAI,CAACK,MAAM,EAAE,CAACrB,QAAQ,CAAC,EAAE,CAAC,CAACY,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EACpD;EAEA;;;EAGQhC,cAAcA,CAAC0C,WAAgB;IACrC,MAAM5C,OAAO,GAA4B,EAAE;IAE3C,IAAI4C,WAAW,IAAIA,WAAW,CAACC,IAAI,EAAE;MACnCD,WAAW,CAACC,IAAI,EAAE,CAACC,OAAO,CAAEC,GAAW,IAAI;QACzC/C,OAAO,CAAC+C,GAAG,CAAC3B,WAAW,EAAE,CAAC,GAAGwB,WAAW,CAACI,GAAG,CAACD,GAAG,CAAC;MACnD,CAAC,CAAC;IACJ;IAEA,OAAO/C,OAAO;EAChB;EAAC,QAAAiD,CAAA,G;qCAvQUxE,eAAe,EAAAyE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAfnF,eAAe;IAAAoF,OAAA,EAAfpF,eAAe,CAAAqF;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}