#!/usr/bin/env python3
"""
Test script to verify frontend compatibility with fixed field names
Tests the exact format that Angular frontend sends
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:3002/api"
HEADERS = {
    'Content-Type': 'application/json',
    'Origin': 'http://localhost:4200',
    'X-Requested-With': 'XMLHttpRequest'
}

def test_signup_with_frontend_format():
    """Test signup with exact frontend field format"""
    print("🧪 Testing Signup with Frontend Field Format...")
    
    # This is the exact format the Angular frontend sends
    signup_data = {
        "firstName": "Frontend",
        "lastName": "Compatible",
        "email": f"frontend_compat_{int(time.time())}@example.com",
        "phone": "+1234567890",
        "password": "FrontendTest123!",
        "confirmPassword": "FrontendTest123!",  # Frontend sends this, not password_confirmation
        "acceptTerms": True
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/signup", json=signup_data, headers=HEADERS, timeout=10)
        print(f"📊 Signup Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 201:
            data = response.json()
            print(f"✅ Signup successful!")
            print(f"🔑 Token: {data.get('token', 'No token')[:50]}...")
            print(f"👤 User: {json.dumps(data.get('user', {}), indent=2)}")
            return data.get('token'), data.get('user')
        elif response.status_code == 422:
            print(f"❌ Validation error: {response.text}")
            try:
                error_data = response.json()
                print(f"🔍 Error details: {json.dumps(error_data, indent=2)}")
            except:
                pass
        else:
            print(f"❌ Signup failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Request error: {e}")
    
    return None, None

def test_verify_email_cors():
    """Test verify-email endpoint CORS"""
    print(f"\n🧪 Testing verify-email CORS...")
    
    # Test OPTIONS preflight request
    try:
        options_response = requests.options(f"{BASE_URL}/auth/verify-email", headers=HEADERS, timeout=5)
        print(f"📊 OPTIONS Status: {options_response.status_code}")
        print(f"📊 CORS Headers: {dict(options_response.headers)}")
        
        if options_response.status_code == 200:
            print(f"✅ CORS preflight successful!")
        else:
            print(f"❌ CORS preflight failed")
            
    except Exception as e:
        print(f"❌ OPTIONS request error: {e}")
    
    # Test POST request with invalid token (should get CORS headers)
    try:
        verify_data = {"token": "invalid-token"}
        post_response = requests.post(f"{BASE_URL}/auth/verify-email", json=verify_data, headers=HEADERS, timeout=5)
        print(f"📊 POST Status: {post_response.status_code}")
        print(f"📊 POST CORS Headers: {dict(post_response.headers)}")
        
        if 'Access-Control-Allow-Origin' in post_response.headers:
            print(f"✅ POST request has CORS headers!")
        else:
            print(f"❌ POST request missing CORS headers")
            
    except Exception as e:
        print(f"❌ POST request error: {e}")

def test_login_with_frontend_format():
    """Test login with frontend format"""
    print(f"\n🧪 Testing Login with Frontend Format...")
    
    # Use existing test user
    login_data = {
        "email": "<EMAIL>",
        "password": "AdminPass123!"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data, headers=HEADERS, timeout=10)
        print(f"📊 Login Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Login successful!")
            print(f"🔑 Token: {data.get('token', 'No token')[:50]}...")
            print(f"👤 User: {json.dumps(data.get('user', {}), indent=2)}")
            return data.get('token')
        else:
            print(f"❌ Login failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Login error: {e}")
    
    return None

def test_protected_endpoints(token):
    """Test protected endpoints with JWT token"""
    print(f"\n🧪 Testing Protected Endpoints...")
    
    if not token:
        print("❌ No token available for testing")
        return
    
    auth_headers = {
        **HEADERS,
        'Authorization': f'Bearer {token}'
    }
    
    # Test profile endpoint
    try:
        response = requests.get(f"{BASE_URL}/auth/profile", headers=auth_headers, timeout=5)
        print(f"📊 Profile Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Profile access successful!")
            print(f"👤 Profile: {json.dumps(data, indent=2)}")
        else:
            print(f"❌ Profile access failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Profile request error: {e}")

def test_password_validation():
    """Test password validation with various scenarios"""
    print(f"\n🧪 Testing Password Validation...")
    
    test_cases = [
        {
            "name": "Missing confirmPassword",
            "data": {
                "firstName": "Test",
                "lastName": "User",
                "email": f"test_missing_confirm_{int(time.time())}@example.com",
                "password": "TestPassword123!"
                # Missing confirmPassword
            }
        },
        {
            "name": "Password mismatch",
            "data": {
                "firstName": "Test",
                "lastName": "User",
                "email": f"test_mismatch_{int(time.time())}@example.com",
                "password": "TestPassword123!",
                "confirmPassword": "DifferentPassword123!"
            }
        },
        {
            "name": "Weak password",
            "data": {
                "firstName": "Test",
                "lastName": "User",
                "email": f"test_weak_{int(time.time())}@example.com",
                "password": "weak",
                "confirmPassword": "weak"
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n  🔍 Testing: {test_case['name']}")
        try:
            response = requests.post(f"{BASE_URL}/auth/signup", json=test_case['data'], headers=HEADERS, timeout=5)
            print(f"    📊 Status: {response.status_code}")
            
            if response.status_code == 422:
                error_data = response.json()
                print(f"    ✅ Validation error as expected: {error_data.get('error', {}).get('message', 'No message')}")
            else:
                print(f"    ❌ Unexpected response: {response.text[:100]}...")
                
        except Exception as e:
            print(f"    ❌ Request error: {e}")

def main():
    """Main test function"""
    print("🚀 Starting Frontend Compatibility Tests")
    print("=" * 60)
    
    # Test server connection
    try:
        response = requests.get("http://localhost:3002", timeout=5)
        print(f"✅ Server running: {response.status_code}")
    except:
        print("❌ Server not running on port 3002")
        return
    
    # Test signup with frontend format
    token, user = test_signup_with_frontend_format()
    
    # Test CORS for verify-email
    test_verify_email_cors()
    
    # Test login
    login_token = test_login_with_frontend_format()
    
    # Test protected endpoints
    test_protected_endpoints(login_token or token)
    
    # Test password validation
    test_password_validation()
    
    print("\n" + "=" * 60)
    print("🏁 Frontend Compatibility Tests Complete")
    
    if token or login_token:
        print("✅ Authentication system is working with frontend format!")
    else:
        print("❌ Authentication issues detected")

if __name__ == "__main__":
    main()
