{"ast": null, "code": "import * as __Ng<PERSON>li_bootstrap_1 from \"@angular/platform-browser\";\nimport { AppModule } from './app/app.module';\nimport { environment } from './environments/environment';\nif (environment.production) {\n  // Disable console logs in production\n  console.log = () => {};\n  console.warn = () => {};\n  console.error = () => {};\n}\n__NgCli_bootstrap_1.platformBrowser().bootstrapModule(AppModule).catch(err => console.error(err));", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}