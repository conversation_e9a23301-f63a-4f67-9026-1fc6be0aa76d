{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/two-factor.service\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/select\";\nfunction Disable2FADialogComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"mat-icon\", 23);\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"All your recovery codes have been used. You can request to disable 2FA via email verification.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction Disable2FADialogComponent_mat_icon_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"hourglass_empty\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction Disable2FADialogComponent_mat_icon_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"email\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let Disable2FADialogComponent = /*#__PURE__*/(() => {\n  class Disable2FADialogComponent {\n    constructor(formBuilder, twoFactorService, snackBar, dialogRef, data) {\n      this.formBuilder = formBuilder;\n      this.twoFactorService = twoFactorService;\n      this.snackBar = snackBar;\n      this.dialogRef = dialogRef;\n      this.data = data;\n      this.loading = false;\n      this.disableForm = this.formBuilder.group({\n        email: [{\n          value: data.email,\n          disabled: true\n        }, [Validators.required, Validators.email]],\n        reason: [data.allCodesUsed ? 'recovery_codes_exhausted' : 'lost_device', [Validators.required]]\n      });\n    }\n    onSubmit() {\n      if (this.disableForm.invalid) {\n        return;\n      }\n      this.loading = true;\n      const formValue = this.disableForm.getRawValue();\n      this.twoFactorService.requestDisable2FA(formValue.email, formValue.reason).subscribe({\n        next: response => {\n          this.snackBar.open('Disable confirmation email sent! Check your inbox and click the link to confirm.', 'Close', {\n            duration: 8000,\n            panelClass: ['success-snackbar']\n          });\n          this.dialogRef.close({\n            success: true,\n            response\n          });\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Failed to request 2FA disable:', error);\n          this.snackBar.open(error.error?.message || 'Failed to send disable email. Please try again.', 'Close', {\n            duration: 5000,\n            panelClass: ['error-snackbar']\n          });\n          this.loading = false;\n        }\n      });\n    }\n    onCancel() {\n      this.dialogRef.close({\n        success: false\n      });\n    }\n    static #_ = this.ɵfac = function Disable2FADialogComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Disable2FADialogComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.TwoFactorService), i0.ɵɵdirectiveInject(i3.MatSnackBar), i0.ɵɵdirectiveInject(i4.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: Disable2FADialogComponent,\n      selectors: [[\"app-disable-2fa-dialog\"]],\n      standalone: false,\n      decls: 65,\n      vars: 7,\n      consts: [[1, \"disable-2fa-dialog\"], [\"mat-dialog-title\", \"\", 1, \"dialog-title\"], [1, \"warning-icon\"], [1, \"dialog-content\"], [1, \"security-warning\"], [1, \"security-icon\"], [1, \"warning-text\"], [\"class\", \"status-info\", 4, \"ngIf\"], [3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"readonly\", \"\"], [\"matSuffix\", \"\"], [\"formControlName\", \"reason\", \"required\", \"\"], [\"value\", \"recovery_codes_exhausted\"], [\"value\", \"lost_device\"], [\"value\", \"other\"], [1, \"alternatives-section\"], [1, \"process-info\"], [1, \"dialog-actions\"], [\"mat-button\", \"\", 3, \"click\", \"disabled\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 3, \"click\", \"disabled\"], [4, \"ngIf\"], [1, \"status-info\"], [1, \"info-icon\"]],\n      template: function Disable2FADialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h2\", 1)(2, \"mat-icon\", 2);\n          i0.ɵɵtext(3, \"warning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(4, \" Disable Two-Factor Authentication \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"mat-dialog-content\", 3)(6, \"div\", 4)(7, \"mat-icon\", 5);\n          i0.ɵɵtext(8, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 6)(10, \"h3\");\n          i0.ɵɵtext(11, \"Security Warning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"p\");\n          i0.ɵɵtext(13, \"Disabling 2FA will make your account less secure. Are you sure you want to proceed?\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(14, Disable2FADialogComponent_div_14_Template, 5, 0, \"div\", 7);\n          i0.ɵɵelementStart(15, \"form\", 8);\n          i0.ɵɵlistener(\"ngSubmit\", function Disable2FADialogComponent_Template_form_ngSubmit_15_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(16, \"mat-form-field\", 9)(17, \"mat-label\");\n          i0.ɵɵtext(18, \"Email Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(19, \"input\", 10);\n          i0.ɵɵelementStart(20, \"mat-icon\", 11);\n          i0.ɵɵtext(21, \"email\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"mat-form-field\", 9)(23, \"mat-label\");\n          i0.ɵɵtext(24, \"Reason for Disabling\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"mat-select\", 12)(26, \"mat-option\", 13);\n          i0.ɵɵtext(27, \"All recovery codes used\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"mat-option\", 14);\n          i0.ɵɵtext(29, \"Lost access to authenticator device\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"mat-option\", 15);\n          i0.ɵɵtext(31, \"Other reason\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"mat-icon\", 11);\n          i0.ɵɵtext(33, \"help_outline\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"div\", 16)(35, \"h4\")(36, \"mat-icon\");\n          i0.ɵɵtext(37, \"lightbulb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(38, \" Consider These Alternatives:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"ul\")(40, \"li\");\n          i0.ɵɵtext(41, \"Generate new recovery codes if you still have access to your authenticator\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"li\");\n          i0.ɵɵtext(43, \"Set up a new authenticator app on a different device\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"li\");\n          i0.ɵɵtext(45, \"Use email-based 2FA verification instead\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(46, \"div\", 17)(47, \"h4\")(48, \"mat-icon\");\n          i0.ɵɵtext(49, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(50, \" Email Verification Process:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"ol\")(52, \"li\");\n          i0.ɵɵtext(53, \"We'll send a secure link to your email address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"li\");\n          i0.ɵɵtext(55, \"Click the link to confirm disabling 2FA\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"li\");\n          i0.ɵɵtext(57, \"The link expires in 1 hour for security\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(58, \"mat-dialog-actions\", 18)(59, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function Disable2FADialogComponent_Template_button_click_59_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵtext(60, \" Cancel \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function Disable2FADialogComponent_Template_button_click_61_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(62, Disable2FADialogComponent_mat_icon_62_Template, 2, 0, \"mat-icon\", 21)(63, Disable2FADialogComponent_mat_icon_63_Template, 2, 0, \"mat-icon\", 21);\n          i0.ɵɵtext(64);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngIf\", ctx.data.allCodesUsed);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.disableForm);\n          i0.ɵɵadvance(44);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.disableForm.invalid || ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.loading ? \"Sending...\" : \"Send Disable Email\", \" \");\n        }\n      },\n      dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, i6.MatFormField, i6.MatLabel, i6.MatSuffix, i7.MatInput, i8.MatButton, i9.MatIcon, i4.MatDialogTitle, i4.MatDialogActions, i4.MatDialogContent, i10.MatSelect, i10.MatOption],\n      styles: [\".disable-2fa-dialog[_ngcontent-%COMP%]{max-width:500px;width:100%}.dialog-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;color:#f57c00;margin-bottom:0}.warning-icon[_ngcontent-%COMP%]{color:#ff9800;font-size:24px;width:24px;height:24px}.dialog-content[_ngcontent-%COMP%]{padding:20px 0}.security-warning[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:16px;background:#fff3e0;border:1px solid #ffcc02;border-radius:8px;padding:16px;margin-bottom:20px}.security-icon[_ngcontent-%COMP%]{color:#ff9800;font-size:32px;width:32px;height:32px;flex-shrink:0}.warning-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 8px;color:#f57c00;font-size:16px}.warning-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:#e65100}.status-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;background:#e3f2fd;border:1px solid #2196f3;border-radius:8px;padding:12px;margin-bottom:20px}.info-icon[_ngcontent-%COMP%]{color:#2196f3}.full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:16px}.alternatives-section[_ngcontent-%COMP%], .process-info[_ngcontent-%COMP%]{background:#f8f9fa;border-radius:8px;padding:16px;margin:16px 0}.alternatives-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .process-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin:0 0 12px;color:#1976d2;font-size:14px}.alternatives-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:0;padding-left:20px}.alternatives-section[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:8px;color:#424242}.process-info[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]{margin:0;padding-left:20px}.process-info[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:8px;color:#424242}.dialog-actions[_ngcontent-%COMP%]{padding:16px 0 0;justify-content:flex-end}.dialog-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-left:8px}\"]\n    });\n  }\n  return Disable2FADialogComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}