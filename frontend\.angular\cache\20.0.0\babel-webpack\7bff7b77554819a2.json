{"ast": null, "code": "import { L as ListKeyManager } from './list-key-manager-C7tp3RbG.mjs';\nclass ActiveDescendantKeyManager extends ListKeyManager {\n  setActiveItem(index) {\n    if (this.activeItem) {\n      this.activeItem.setInactiveStyles();\n    }\n    super.setActiveItem(index);\n    if (this.activeItem) {\n      this.activeItem.setActiveStyles();\n    }\n  }\n}\nexport { ActiveDescendantKeyManager as A };\n//# sourceMappingURL=activedescendant-key-manager-CZAE5aFC.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}