{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nexport let TwoFactorService = /*#__PURE__*/(() => {\n  class TwoFactorService {\n    constructor(http, authService) {\n      this.http = http;\n      this.authService = authService;\n    }\n    setup2FA() {\n      return this.http.post(`${environment.apiUrl}/2fa/setup`, {}, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(catchError(this.handleError));\n    }\n    verify2FA(code, method = 'authenticator') {\n      return this.http.post(`${environment.apiUrl}/2fa/verify`, {\n        token: code,\n        method\n      }, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(catchError(this.handleError));\n    }\n    disable2FA(code, password) {\n      const body = {\n        token: code\n      };\n      if (password) {\n        body.password = password;\n      }\n      return this.http.post(`${environment.apiUrl}/2fa/disable`, body, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(catchError(this.handleError));\n    }\n    get2FAStatus() {\n      return this.http.get(`${environment.apiUrl}/2fa/status`, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(catchError(this.handleError));\n    }\n    send2FASMS() {\n      return this.http.post(`${environment.apiUrl}/2fa/send-sms`, {}, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(catchError(this.handleError));\n    }\n    verify2FASMS(code) {\n      return this.http.post(`${environment.apiUrl}/2fa/verify-sms`, {\n        code\n      }, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(catchError(this.handleError));\n    }\n    send2FAEmail() {\n      return this.http.post(`${environment.apiUrl}/2fa/send-email`, {}, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(catchError(this.handleError));\n    }\n    verify2FAEmail(code) {\n      return this.http.post(`${environment.apiUrl}/2fa/verify-email`, {\n        code\n      }, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(catchError(this.handleError));\n    }\n    validateTOTPCode(code) {\n      // Basic validation for TOTP codes\n      return /^\\d{6}$/.test(code);\n    }\n    generateBackupCodes() {\n      // Generate backup codes for 2FA\n      const codes = [];\n      for (let i = 0; i < 10; i++) {\n        const code = Math.random().toString(36).substring(2, 10).toUpperCase();\n        codes.push(code);\n      }\n      return codes;\n    }\n    downloadBackupCodes(codes) {\n      const content = `SecureApp - Two-Factor Authentication Backup Codes\nGenerated on: ${new Date().toLocaleString()}\n\nIMPORTANT: Store these codes in a safe place. Each code can only be used once.\n\n${codes.map((code, index) => `${index + 1}. ${code}`).join('\\n')}\n\nInstructions:\n- Use these codes if you lose access to your authenticator app\n- Each code can only be used once\n- Generate new codes if you use all of them\n- Keep these codes secure and private`;\n      const blob = new Blob([content], {\n        type: 'text/plain'\n      });\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = `secureapp-backup-codes-${Date.now()}.txt`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    }\n    // New 2FA Disable Methods\n    requestDisable2FA(email, reason) {\n      const body = {\n        email\n      };\n      if (reason) {\n        body.reason = reason;\n      }\n      return this.http.post(`${environment.apiUrl}/auth/request-disable-2fa`, body).pipe(catchError(this.handleError));\n    }\n    confirmDisable2FA(token) {\n      return this.http.post(`${environment.apiUrl}/auth/confirm-disable-2fa`, {\n        token\n      }).pipe(catchError(this.handleError));\n    }\n    checkRecoveryCodesStatus() {\n      return this.http.get(`${environment.apiUrl}/2fa/recovery-status`, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(catchError(this.handleError));\n    }\n    handleError(error) {\n      let errorMessage = 'Two-factor authentication error occurred';\n      if (error.error instanceof ErrorEvent) {\n        errorMessage = error.error.message;\n      } else {\n        errorMessage = error.error?.message || error.message || `Error Code: ${error.status}`;\n      }\n      console.error('Two-Factor Service Error:', error);\n      return throwError(() => new Error(errorMessage));\n    }\n    static #_ = this.ɵfac = function TwoFactorService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TwoFactorService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TwoFactorService,\n      factory: TwoFactorService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return TwoFactorService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}