{"ast": null, "code": "import { switchMap } from './switchMap';\nimport { operate } from '../util/lift';\nexport function switchScan(accumulator, seed) {\n  return operate((source, subscriber) => {\n    let state = seed;\n    switchMap((value, index) => accumulator(state, value, index), (_, innerValue) => (state = innerValue, innerValue))(source).subscribe(subscriber);\n    return () => {\n      state = null;\n    };\n  });\n}\n//# sourceMappingURL=switchScan.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}