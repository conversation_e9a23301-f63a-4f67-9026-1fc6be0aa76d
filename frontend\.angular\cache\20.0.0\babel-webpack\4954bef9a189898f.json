{"ast": null, "code": "import { HttpEventType } from '@angular/common/http';\nimport { throwError } from 'rxjs';\nimport { catchError, finalize, tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../services/loading.service\";\nimport * as i4 from \"../services/rate-limit.service\";\nexport let AuthInterceptor = /*#__PURE__*/(() => {\n  class AuthInterceptor {\n    constructor(authService, router, loadingService, rateLimitService) {\n      this.authService = authService;\n      this.router = router;\n      this.loadingService = loadingService;\n      this.rateLimitService = rateLimitService;\n    }\n    intercept(request, next) {\n      // Show loading indicator\n      this.loadingService.show();\n      // Add auth token if available\n      const token = this.authService.getToken();\n      if (token && !this.authService.isTokenExpired()) {\n        request = request.clone({\n          setHeaders: {\n            Authorization: `Bearer ${token}`,\n            'Content-Type': 'application/json',\n            'X-Requested-With': 'XMLHttpRequest'\n          }\n        });\n      }\n      // Add CSRF protection\n      request = request.clone({\n        setHeaders: {\n          'X-CSRF-Token': this.generateCSRFToken()\n        }\n      });\n      return next.handle(request).pipe(tap(event => {\n        // Handle successful responses and extract rate limit headers\n        if (event.type === HttpEventType.Response) {\n          const response = event;\n          if (response.headers) {\n            this.rateLimitService.handleRateLimitResponse(this.extractHeaders(response.headers));\n          }\n        }\n      }), catchError(error => {\n        this.handleError(error);\n        return throwError(() => error);\n      }), finalize(() => {\n        // Hide loading indicator\n        this.loadingService.hide();\n      }));\n    }\n    handleError(error) {\n      console.log('🚨 HTTP Error intercepted:', error);\n      console.log('🔍 Error details:', {\n        status: error.status,\n        message: error.message,\n        url: error.url,\n        statusText: error.statusText,\n        error: error.error\n      });\n      // Enhanced rate limit detection for all error types (must be first)\n      if (this.detectRateLimitError(error)) {\n        console.warn('🚦 Rate limit detected via enhanced analysis - triggering popup');\n        this.handleRateLimitError(error);\n        return; // Don't continue with other error handling\n      } else {\n        console.log('❌ Rate limit NOT detected - continuing with normal error handling');\n      }\n      switch (error.status) {\n        case 401:\n          // Check if this is an account lockout vs. token expiry\n          const errorMessage = error.error?.error?.message || error.error?.message || '';\n          if (errorMessage.includes('temporarily locked') || errorMessage.includes('multiple failed login attempts')) {\n            // This is an account lockout - don't auto-logout, let the login component handle it\n            console.log('Account lockout detected - allowing login component to handle');\n            return;\n          }\n          // Regular unauthorized - token expired or invalid\n          this.authService.logout();\n          this.router.navigate(['/auth/login'], {\n            queryParams: {\n              message: 'Session expired. Please login again.'\n            }\n          });\n          break;\n        case 403:\n          // Forbidden - insufficient permissions\n          this.router.navigate(['/unauthorized']);\n          break;\n        case 429:\n          // Too many requests - should already be handled by enhanced detection above\n          console.warn('🚦 Direct 429 status - already handled by enhanced detection');\n          break;\n        case 0:\n          // Network error - could be rate limit with CORS issues (handled by enhanced detection)\n          console.error('🌐 Network error. Please check your connection.');\n          break;\n        default:\n          console.error('❌ HTTP Error:', error);\n      }\n    }\n    /**\n     * Enhanced rate limit detection from various error patterns\n     * Now specifically searches for \"rate\" in error messages to avoid false positives\n     */\n    detectRateLimitError(error) {\n      // Direct 429 status\n      if (error.status === 429) return true;\n      // Check error message for rate limit indicators\n      const errorMessage = error.message?.toLowerCase() || '';\n      const errorText = error.error?.toString?.()?.toLowerCase() || '';\n      const errorDetails = error.error?.error?.message?.toLowerCase() || '';\n      const statusText = error.statusText?.toLowerCase() || '';\n      const url = error.url?.toLowerCase() || '';\n      // More specific rate limit indicators - must contain \"rate\" or very specific rate limit terms\n      const rateLimitIndicators = ['too many requests', '(too many requests)',\n      // Your specific console error format\n      'rate limit', 'ratelimit', 'rate-limit', 'rate exceeded', 'quota exceeded', 'throttled', 'throttle'];\n      // Check if error contains specific rate limit indicators\n      const hasRateLimitPattern = rateLimitIndicators.some(indicator => errorMessage.includes(indicator) || errorText.includes(indicator) || errorDetails.includes(indicator) || statusText.includes(indicator));\n      // Only check for specific rate-related network failures\n      // Must have \"rate\" in the error message to be considered a rate limit\n      const hasRateInMessage = errorMessage.includes('rate') || errorText.includes('rate') || errorDetails.includes('rate') || statusText.includes('rate');\n      // Network failure that specifically mentions rate limiting\n      const hasNetworkFailureWithRate = (error.status === 0 || errorMessage.includes('net::err_failed')) && hasRateInMessage;\n      // Log detection for debugging\n      if (hasRateLimitPattern || hasNetworkFailureWithRate) {\n        console.log('🚦 Rate limit detected:', {\n          status: error.status,\n          message: errorMessage,\n          text: errorText,\n          details: errorDetails,\n          statusText: statusText,\n          url: url,\n          rateLimitPattern: hasRateLimitPattern,\n          networkFailureWithRate: hasNetworkFailureWithRate,\n          hasRateInMessage: hasRateInMessage\n        });\n        return true;\n      }\n      console.log('❌ Rate limit NOT detected - error does not contain rate limit indicators:', {\n        status: error.status,\n        message: errorMessage.substring(0, 100) + '...',\n        hasRateInMessage: hasRateInMessage\n      });\n      return false;\n    }\n    /**\n     * Handle rate limit error with smart fallback data\n     */\n    handleRateLimitError(error) {\n      console.log('🚦 Handling rate limit error:', error);\n      // Try to extract headers if available\n      let headers = {};\n      if (error.headers) {\n        headers = this.extractHeaders(error.headers);\n      }\n      // Check if we have actual rate limit headers from the server\n      const hasRateLimitHeaders = headers['retry-after'] || headers['x-ratelimit-retryafter'] || headers['x-ratelimit-limit'] || headers['x-ratelimit-remaining'];\n      // If no rate limit headers but we detected a rate limit, provide smart fallback values\n      if (!hasRateLimitHeaders) {\n        headers = this.generateSmartRateLimitHeaders(error.url || '');\n        console.log('🔄 Using smart fallback rate limit headers:', headers);\n      } else {\n        console.log('✅ Using actual rate limit headers from server:', headers);\n      }\n      // Handle the rate limit response\n      this.rateLimitService.handleRateLimitResponse(headers, error);\n    }\n    /**\n     * Generate smart fallback rate limit headers based on URL patterns\n     */\n    generateSmartRateLimitHeaders(url) {\n      const now = Math.floor(Date.now() / 1000);\n      // Match backend configuration: RATE_LIMIT_WINDOW=1 minute, RATE_LIMIT_MAX=2\n      let retryAfter = 60; // 1 minute to match backend\n      let limit = 2; // Match backend RATE_LIMIT_MAX=2\n      if (url.includes('/auth/login')) {\n        retryAfter = 60; // 1 minute for login attempts (match backend)\n        limit = 2;\n      } else if (url.includes('/auth/register')) {\n        retryAfter = 60; // 1 minute for registration\n        limit = 2;\n      } else if (url.includes('/auth/reset') || url.includes('/auth/forgot')) {\n        retryAfter = 60; // 1 minute for password reset\n        limit = 2;\n      } else if (url.includes('/auth/verify')) {\n        retryAfter = 60; // 1 minute for verification codes\n        limit = 2;\n      } else if (url.includes('/auth/')) {\n        retryAfter = 60; // 1 minute for other auth endpoints\n        limit = 2;\n      }\n      return {\n        'retry-after': retryAfter.toString(),\n        'x-ratelimit-limit': limit.toString(),\n        'x-ratelimit-remaining': '0',\n        'x-ratelimit-reset': (now + retryAfter).toString(),\n        'x-ratelimit-totalrequests': limit.toString()\n      };\n    }\n    generateCSRFToken() {\n      // Simple CSRF token generation\n      return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n    }\n    /**\n     * Extract headers from HttpHeaders object\n     */\n    extractHeaders(httpHeaders) {\n      const headers = {};\n      if (httpHeaders && httpHeaders.keys) {\n        httpHeaders.keys().forEach(key => {\n          headers[key.toLowerCase()] = httpHeaders.get(key);\n        });\n      }\n      return headers;\n    }\n    static #_ = this.ɵfac = function AuthInterceptor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthInterceptor)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.LoadingService), i0.ɵɵinject(i4.RateLimitService));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthInterceptor,\n      factory: AuthInterceptor.ɵfac\n    });\n  }\n  return AuthInterceptor;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}