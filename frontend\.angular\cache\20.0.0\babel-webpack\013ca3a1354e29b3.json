{"ast": null, "code": "import { _IdGenerator } from '@angular/cdk/a11y';\nimport { ENTER, SPACE, hasModifier<PERSON>ey } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, ElementRef, ChangeDetectorRef, signal, EventEmitter, isSignal, Output, ViewChild } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { M as MatRipple } from './ripple-BYgV4oZC.mjs';\nimport { M as MatPseudoCheckbox } from './pseudo-checkbox-DDmgx3P4.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { _CdkPrivateStyleLoader, _VisuallyHiddenLoader } from '@angular/cdk/private';\n\n/**\n * Injection token used to provide the parent component to options.\n */\nconst _c0 = [\"*\", [[\"mat-option\"], [\"ng-container\"]]];\nconst _c1 = [\"*\", \"mat-option, ng-container\"];\nconst _c2 = [\"text\"];\nconst _c3 = [[[\"mat-icon\"]], \"*\"];\nconst _c4 = [\"mat-icon\", \"*\"];\nfunction MatOption_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 1);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.disabled)(\"state\", ctx_r0.selected ? \"checked\" : \"unchecked\");\n  }\n}\nfunction MatOption_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-pseudo-checkbox\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.disabled);\n  }\n}\nfunction MatOption_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r0.group.label, \")\");\n  }\n}\nconst MAT_OPTION_PARENT_COMPONENT = /*#__PURE__*/new InjectionToken('MAT_OPTION_PARENT_COMPONENT');\n\n// Notes on the accessibility pattern used for `mat-optgroup`.\n// The option group has two different \"modes\": regular and inert. The regular mode uses the\n// recommended a11y pattern which has `role=\"group\"` on the group element with `aria-labelledby`\n// pointing to the label. This works for `mat-select`, but it seems to hit a bug for autocomplete\n// under VoiceOver where the group doesn't get read out at all. The bug appears to be that if\n// there's __any__ a11y-related attribute on the group (e.g. `role` or `aria-labelledby`),\n// VoiceOver on Safari won't read it out.\n// We've introduced the `inert` mode as a workaround. Under this mode, all a11y attributes are\n// removed from the group, and we get the screen reader to read out the group label by mirroring it\n// inside an invisible element in the option. This is sub-optimal, because the screen reader will\n// repeat the group label on each navigation, whereas the default pattern only reads the group when\n// the user enters a new group. The following alternate approaches were considered:\n// 1. Reading out the group label using the `LiveAnnouncer` solves the problem, but we can't control\n//    when the text will be read out so sometimes it comes in too late or never if the user\n//    navigates quickly.\n// 2. `<mat-option aria-describedby=\"groupLabel\"` - This works on Safari, but VoiceOver in Chrome\n//    won't read out the description at all.\n// 3. `<mat-option aria-labelledby=\"optionLabel groupLabel\"` - This works on Chrome, but Safari\n//     doesn't read out the text at all. Furthermore, on\n/**\n * Injection token that can be used to reference instances of `MatOptgroup`. It serves as\n * alternative token to the actual `MatOptgroup` class which could cause unnecessary\n * retention of the class and its component metadata.\n */\nconst MAT_OPTGROUP = /*#__PURE__*/new InjectionToken('MatOptgroup');\n/**\n * Component that is used to group instances of `mat-option`.\n */\nlet MatOptgroup = /*#__PURE__*/(() => {\n  class MatOptgroup {\n    /** Label for the option group. */\n    label;\n    /** whether the option group is disabled. */\n    disabled = false;\n    /** Unique id for the underlying label. */\n    _labelId = inject(_IdGenerator).getId('mat-optgroup-label-');\n    /** Whether the group is in inert a11y mode. */\n    _inert;\n    constructor() {\n      const parent = inject(MAT_OPTION_PARENT_COMPONENT, {\n        optional: true\n      });\n      this._inert = parent?.inertGroups ?? false;\n    }\n    static ɵfac = function MatOptgroup_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatOptgroup)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatOptgroup,\n      selectors: [[\"mat-optgroup\"]],\n      hostAttrs: [1, \"mat-mdc-optgroup\"],\n      hostVars: 3,\n      hostBindings: function MatOptgroup_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"role\", ctx._inert ? null : \"group\")(\"aria-disabled\", ctx._inert ? null : ctx.disabled.toString())(\"aria-labelledby\", ctx._inert ? null : ctx._labelId);\n        }\n      },\n      inputs: {\n        label: \"label\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n      },\n      exportAs: [\"matOptgroup\"],\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_OPTGROUP,\n        useExisting: MatOptgroup\n      }])],\n      ngContentSelectors: _c1,\n      decls: 5,\n      vars: 4,\n      consts: [[\"role\", \"presentation\", 1, \"mat-mdc-optgroup-label\", 3, \"id\"], [1, \"mdc-list-item__primary-text\"]],\n      template: function MatOptgroup_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"span\", 0)(1, \"span\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵprojection(3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵprojection(4, 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mdc-list-item--disabled\", ctx.disabled);\n          i0.ɵɵproperty(\"id\", ctx._labelId);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"\", ctx.label, \" \");\n        }\n      },\n      styles: [\".mat-mdc-optgroup{color:var(--mat-optgroup-label-text-color, var(--mat-sys-on-surface-variant));font-family:var(--mat-optgroup-label-text-font, var(--mat-sys-title-small-font));line-height:var(--mat-optgroup-label-text-line-height, var(--mat-sys-title-small-line-height));font-size:var(--mat-optgroup-label-text-size, var(--mat-sys-title-small-size));letter-spacing:var(--mat-optgroup-label-text-tracking, var(--mat-sys-title-small-tracking));font-weight:var(--mat-optgroup-label-text-weight, var(--mat-sys-title-small-weight))}.mat-mdc-optgroup-label{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;outline:none}.mat-mdc-optgroup-label.mdc-list-item--disabled{opacity:.38}.mat-mdc-optgroup-label .mdc-list-item__primary-text{font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;white-space:normal;color:inherit}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatOptgroup;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Event object emitted by MatOption when selected or deselected. */\nclass MatOptionSelectionChange {\n  source;\n  isUserInput;\n  constructor(/** Reference to the option that emitted the event. */\n  source, /** Whether the change in the option's value was a result of a user action. */\n  isUserInput = false) {\n    this.source = source;\n    this.isUserInput = isUserInput;\n  }\n}\n/**\n * Single option inside of a `<mat-select>` element.\n */\nlet MatOption = /*#__PURE__*/(() => {\n  class MatOption {\n    _element = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _parent = inject(MAT_OPTION_PARENT_COMPONENT, {\n      optional: true\n    });\n    group = inject(MAT_OPTGROUP, {\n      optional: true\n    });\n    _signalDisableRipple = false;\n    _selected = false;\n    _active = false;\n    _mostRecentViewValue = '';\n    /** Whether the wrapping component is in multiple selection mode. */\n    get multiple() {\n      return this._parent && this._parent.multiple;\n    }\n    /** Whether or not the option is currently selected. */\n    get selected() {\n      return this._selected;\n    }\n    /** The form value of the option. */\n    value;\n    /** The unique ID of the option. */\n    id = inject(_IdGenerator).getId('mat-option-');\n    /** Whether the option is disabled. */\n    get disabled() {\n      return this.group && this.group.disabled || this._disabled();\n    }\n    set disabled(value) {\n      this._disabled.set(value);\n    }\n    _disabled = signal(false);\n    /** Whether ripples for the option are disabled. */\n    get disableRipple() {\n      return this._signalDisableRipple ? this._parent.disableRipple() : !!this._parent?.disableRipple;\n    }\n    /** Whether to display checkmark for single-selection. */\n    get hideSingleSelectionIndicator() {\n      return !!(this._parent && this._parent.hideSingleSelectionIndicator);\n    }\n    /** Event emitted when the option is selected or deselected. */\n    // tslint:disable-next-line:no-output-on-prefix\n    onSelectionChange = new EventEmitter();\n    /** Element containing the option's text. */\n    _text;\n    /** Emits when the state of the option changes and any parents have to be notified. */\n    _stateChanges = new Subject();\n    constructor() {\n      const styleLoader = inject(_CdkPrivateStyleLoader);\n      styleLoader.load(_StructuralStylesLoader);\n      styleLoader.load(_VisuallyHiddenLoader);\n      this._signalDisableRipple = !!this._parent && isSignal(this._parent.disableRipple);\n    }\n    /**\n     * Whether or not the option is currently active and ready to be selected.\n     * An active option displays styles as if it is focused, but the\n     * focus is actually retained somewhere else. This comes in handy\n     * for components like autocomplete where focus must remain on the input.\n     */\n    get active() {\n      return this._active;\n    }\n    /**\n     * The displayed value of the option. It is necessary to show the selected option in the\n     * select's trigger.\n     */\n    get viewValue() {\n      // TODO(kara): Add input property alternative for node envs.\n      return (this._text?.nativeElement.textContent || '').trim();\n    }\n    /** Selects the option. */\n    select(emitEvent = true) {\n      if (!this._selected) {\n        this._selected = true;\n        this._changeDetectorRef.markForCheck();\n        if (emitEvent) {\n          this._emitSelectionChangeEvent();\n        }\n      }\n    }\n    /** Deselects the option. */\n    deselect(emitEvent = true) {\n      if (this._selected) {\n        this._selected = false;\n        this._changeDetectorRef.markForCheck();\n        if (emitEvent) {\n          this._emitSelectionChangeEvent();\n        }\n      }\n    }\n    /** Sets focus onto this option. */\n    focus(_origin, options) {\n      // Note that we aren't using `_origin`, but we need to keep it because some internal consumers\n      // use `MatOption` in a `FocusKeyManager` and we need it to match `FocusableOption`.\n      const element = this._getHostElement();\n      if (typeof element.focus === 'function') {\n        element.focus(options);\n      }\n    }\n    /**\n     * This method sets display styles on the option to make it appear\n     * active. This is used by the ActiveDescendantKeyManager so key\n     * events will display the proper options as active on arrow key events.\n     */\n    setActiveStyles() {\n      if (!this._active) {\n        this._active = true;\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    /**\n     * This method removes display styles on the option that made it appear\n     * active. This is used by the ActiveDescendantKeyManager so key\n     * events will display the proper options as active on arrow key events.\n     */\n    setInactiveStyles() {\n      if (this._active) {\n        this._active = false;\n        this._changeDetectorRef.markForCheck();\n      }\n    }\n    /** Gets the label to be used when determining whether the option should be focused. */\n    getLabel() {\n      return this.viewValue;\n    }\n    /** Ensures the option is selected when activated from the keyboard. */\n    _handleKeydown(event) {\n      if ((event.keyCode === ENTER || event.keyCode === SPACE) && !hasModifierKey(event)) {\n        this._selectViaInteraction();\n        // Prevent the page from scrolling down and form submits.\n        event.preventDefault();\n      }\n    }\n    /**\n     * `Selects the option while indicating the selection came from the user. Used to\n     * determine if the select's view -> model callback should be invoked.`\n     */\n    _selectViaInteraction() {\n      if (!this.disabled) {\n        this._selected = this.multiple ? !this._selected : true;\n        this._changeDetectorRef.markForCheck();\n        this._emitSelectionChangeEvent(true);\n      }\n    }\n    /** Returns the correct tabindex for the option depending on disabled state. */\n    // This method is only used by `MatLegacyOption`. Keeping it here to avoid breaking the types.\n    // That's because `MatLegacyOption` use `MatOption` type in a few places such as\n    // `MatOptionSelectionChange`. It is safe to delete this when `MatLegacyOption` is deleted.\n    _getTabIndex() {\n      return this.disabled ? '-1' : '0';\n    }\n    /** Gets the host DOM element. */\n    _getHostElement() {\n      return this._element.nativeElement;\n    }\n    ngAfterViewChecked() {\n      // Since parent components could be using the option's label to display the selected values\n      // (e.g. `mat-select`) and they don't have a way of knowing if the option's label has changed\n      // we have to check for changes in the DOM ourselves and dispatch an event. These checks are\n      // relatively cheap, however we still limit them only to selected options in order to avoid\n      // hitting the DOM too often.\n      if (this._selected) {\n        const viewValue = this.viewValue;\n        if (viewValue !== this._mostRecentViewValue) {\n          if (this._mostRecentViewValue) {\n            this._stateChanges.next();\n          }\n          this._mostRecentViewValue = viewValue;\n        }\n      }\n    }\n    ngOnDestroy() {\n      this._stateChanges.complete();\n    }\n    /** Emits the selection change event. */\n    _emitSelectionChangeEvent(isUserInput = false) {\n      this.onSelectionChange.emit(new MatOptionSelectionChange(this, isUserInput));\n    }\n    static ɵfac = function MatOption_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatOption)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatOption,\n      selectors: [[\"mat-option\"]],\n      viewQuery: function MatOption_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c2, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._text = _t.first);\n        }\n      },\n      hostAttrs: [\"role\", \"option\", 1, \"mat-mdc-option\", \"mdc-list-item\"],\n      hostVars: 11,\n      hostBindings: function MatOption_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatOption_click_HostBindingHandler() {\n            return ctx._selectViaInteraction();\n          })(\"keydown\", function MatOption_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵdomProperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"aria-selected\", ctx.selected)(\"aria-disabled\", ctx.disabled.toString());\n          i0.ɵɵclassProp(\"mdc-list-item--selected\", ctx.selected)(\"mat-mdc-option-multiple\", ctx.multiple)(\"mat-mdc-option-active\", ctx.active)(\"mdc-list-item--disabled\", ctx.disabled);\n        }\n      },\n      inputs: {\n        value: \"value\",\n        id: \"id\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n      },\n      outputs: {\n        onSelectionChange: \"onSelectionChange\"\n      },\n      exportAs: [\"matOption\"],\n      ngContentSelectors: _c4,\n      decls: 8,\n      vars: 5,\n      consts: [[\"text\", \"\"], [\"aria-hidden\", \"true\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\", \"state\"], [1, \"mdc-list-item__primary-text\"], [\"state\", \"checked\", \"aria-hidden\", \"true\", \"appearance\", \"minimal\", 1, \"mat-mdc-option-pseudo-checkbox\", 3, \"disabled\"], [1, \"cdk-visually-hidden\"], [\"aria-hidden\", \"true\", \"mat-ripple\", \"\", 1, \"mat-mdc-option-ripple\", \"mat-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\"]],\n      template: function MatOption_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c3);\n          i0.ɵɵconditionalCreate(0, MatOption_Conditional_0_Template, 1, 2, \"mat-pseudo-checkbox\", 1);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementStart(2, \"span\", 2, 0);\n          i0.ɵɵprojection(4, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵconditionalCreate(5, MatOption_Conditional_5_Template, 1, 1, \"mat-pseudo-checkbox\", 3);\n          i0.ɵɵconditionalCreate(6, MatOption_Conditional_6_Template, 2, 1, \"span\", 4);\n          i0.ɵɵelement(7, \"div\", 5);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(ctx.multiple ? 0 : -1);\n          i0.ɵɵadvance(5);\n          i0.ɵɵconditional(!ctx.multiple && ctx.selected && !ctx.hideSingleSelectionIndicator ? 5 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.group && ctx.group._inert ? 6 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRippleTrigger\", ctx._getHostElement())(\"matRippleDisabled\", ctx.disabled || ctx.disableRipple);\n        }\n      },\n      dependencies: [MatPseudoCheckbox, MatRipple],\n      styles: [\".mat-mdc-option{-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;min-height:48px;padding:0 16px;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color, var(--mat-sys-on-surface));font-family:var(--mat-option-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mat-option-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mat-option-label-text-size, var(--mat-sys-body-large-size));letter-spacing:var(--mat-option-label-text-tracking, var(--mat-sys-label-large-tracking));font-weight:var(--mat-option-label-text-weight, var(--mat-sys-body-large-weight))}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-hover-state-layer-opacity) * 100%), transparent))}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color, color-mix(in srgb, var(--mat-sys-on-surface) calc(var(--mat-sys-focus-state-layer-opacity) * 100%), transparent));outline:0}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color, var(--mat-sys-secondary-container))}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option .mat-pseudo-checkbox{--mat-pseudo-checkbox-minimal-selected-checkmark-color: var(--mat-option-selected-state-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}@media(forced-colors: active){.mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{content:\\\"\\\";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .mat-mdc-option.mdc-list-item--selected:not(:has(.mat-mdc-option-pseudo-checkbox))::after{right:auto;left:16px}}.mat-mdc-option-multiple{--mat-list-list-item-selected-container-color: var(--mat-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-focus-indicator::before{content:\\\"\\\"}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatOption;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Counts the amount of option group labels that precede the specified option.\n * @param optionIndex Index of the option at which to start counting.\n * @param options Flat list of all of the options.\n * @param optionGroups Flat list of all of the option groups.\n * @docs-private\n */\nfunction _countGroupLabelsBeforeOption(optionIndex, options, optionGroups) {\n  if (optionGroups.length) {\n    let optionsArray = options.toArray();\n    let groups = optionGroups.toArray();\n    let groupCounter = 0;\n    for (let i = 0; i < optionIndex + 1; i++) {\n      if (optionsArray[i].group && optionsArray[i].group === groups[groupCounter]) {\n        groupCounter++;\n      }\n    }\n    return groupCounter;\n  }\n  return 0;\n}\n/**\n * Determines the position to which to scroll a panel in order for an option to be into view.\n * @param optionOffset Offset of the option from the top of the panel.\n * @param optionHeight Height of the options.\n * @param currentScrollPosition Current scroll position of the panel.\n * @param panelHeight Height of the panel.\n * @docs-private\n */\nfunction _getOptionScrollPosition(optionOffset, optionHeight, currentScrollPosition, panelHeight) {\n  if (optionOffset < currentScrollPosition) {\n    return optionOffset;\n  }\n  if (optionOffset + optionHeight > currentScrollPosition + panelHeight) {\n    return Math.max(0, optionOffset - panelHeight + optionHeight);\n  }\n  return currentScrollPosition;\n}\nexport { MatOption as M, _countGroupLabelsBeforeOption as _, MatOptgroup as a, _getOptionScrollPosition as b, MAT_OPTION_PARENT_COMPONENT as c, MAT_OPTGROUP as d, MatOptionSelectionChange as e };\n//# sourceMappingURL=option-BzhYL_xC.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}