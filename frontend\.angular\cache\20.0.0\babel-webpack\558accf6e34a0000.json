{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, NgZone, Renderer2, booleanAttribute, Directive, Input, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\nimport { FocusMonitor } from '@angular/cdk/a11y';\nimport { _CdkPrivateStyleLoader } from '@angular/cdk/private';\nimport { M as MatRippleLoader } from './ripple-loader-BnMiRtmT.mjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-CObeNzjn.mjs';\nimport { _ as _animationsDisabled } from './animation-DfMFjxHu.mjs';\n\n/** Injection token that can be used to provide the default options the button component. */\nconst _c0 = [\"mat-icon-button\", \"\"];\nconst _c1 = [\"*\"];\nconst MAT_BUTTON_CONFIG = /*#__PURE__*/new InjectionToken('MAT_BUTTON_CONFIG');\nfunction transformTabIndex(value) {\n  return value == null ? undefined : numberAttribute(value);\n}\n/** Base class for all buttons. */\nlet MatButtonBase = /*#__PURE__*/(() => {\n  class MatButtonBase {\n    _elementRef = inject(ElementRef);\n    _ngZone = inject(NgZone);\n    _animationsDisabled = _animationsDisabled();\n    _config = inject(MAT_BUTTON_CONFIG, {\n      optional: true\n    });\n    _focusMonitor = inject(FocusMonitor);\n    _cleanupClick;\n    _renderer = inject(Renderer2);\n    /**\n     * Handles the lazy creation of the MatButton ripple.\n     * Used to improve initial load time of large applications.\n     */\n    _rippleLoader = inject(MatRippleLoader);\n    /** Whether the button is set on an anchor node. */\n    _isAnchor;\n    /** Whether this button is a FAB. Used to apply the correct class on the ripple. */\n    _isFab = false;\n    /**\n     * Theme color of the button. This API is supported in M2 themes only, it has\n     * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/button/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /** Whether the ripple effect is disabled or not. */\n    get disableRipple() {\n      return this._disableRipple;\n    }\n    set disableRipple(value) {\n      this._disableRipple = value;\n      this._updateRippleDisabled();\n    }\n    _disableRipple = false;\n    /** Whether the button is disabled. */\n    get disabled() {\n      return this._disabled;\n    }\n    set disabled(value) {\n      this._disabled = value;\n      this._updateRippleDisabled();\n    }\n    _disabled = false;\n    /** `aria-disabled` value of the button. */\n    ariaDisabled;\n    /**\n     * Natively disabled buttons prevent focus and any pointer events from reaching the button.\n     * In some scenarios this might not be desirable, because it can prevent users from finding out\n     * why the button is disabled (e.g. via tooltip). This is also useful for buttons that may\n     * become disabled when activated, which would cause focus to be transferred to the document\n     * body instead of remaining on the button.\n     *\n     * Enabling this input will change the button so that it is styled to be disabled and will be\n     * marked as `aria-disabled`, but it will allow the button to receive events and focus.\n     *\n     * Note that by enabling this, you need to set the `tabindex` yourself if the button isn't\n     * meant to be tabbable and you have to prevent the button action (e.g. form submissions).\n     */\n    disabledInteractive;\n    /** Tab index for the button. */\n    tabIndex;\n    /**\n     * Backwards-compatibility input that handles pre-existing `[tabindex]` bindings.\n     * @docs-private\n     */\n    set _tabindex(value) {\n      this.tabIndex = value;\n    }\n    constructor() {\n      inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n      const element = this._elementRef.nativeElement;\n      this._isAnchor = element.tagName === 'A';\n      this.disabledInteractive = this._config?.disabledInteractive ?? false;\n      this.color = this._config?.color ?? null;\n      this._rippleLoader?.configureRipple(element, {\n        className: 'mat-mdc-button-ripple'\n      });\n    }\n    ngAfterViewInit() {\n      this._focusMonitor.monitor(this._elementRef, true);\n      // Some internal tests depend on the timing of this,\n      // otherwise we could bind it in the constructor.\n      if (this._isAnchor) {\n        this._setupAsAnchor();\n      }\n    }\n    ngOnDestroy() {\n      this._cleanupClick?.();\n      this._focusMonitor.stopMonitoring(this._elementRef);\n      this._rippleLoader?.destroyRipple(this._elementRef.nativeElement);\n    }\n    /** Focuses the button. */\n    focus(origin = 'program', options) {\n      if (origin) {\n        this._focusMonitor.focusVia(this._elementRef.nativeElement, origin, options);\n      } else {\n        this._elementRef.nativeElement.focus(options);\n      }\n    }\n    _getAriaDisabled() {\n      if (this.ariaDisabled != null) {\n        return this.ariaDisabled;\n      }\n      if (this._isAnchor) {\n        return this.disabled || null;\n      }\n      return this.disabled && this.disabledInteractive ? true : null;\n    }\n    _getDisabledAttribute() {\n      return this.disabledInteractive || !this.disabled ? null : true;\n    }\n    _updateRippleDisabled() {\n      this._rippleLoader?.setDisabled(this._elementRef.nativeElement, this.disableRipple || this.disabled);\n    }\n    _getTabIndex() {\n      if (this._isAnchor) {\n        return this.disabled && !this.disabledInteractive ? -1 : this.tabIndex;\n      }\n      return this.tabIndex;\n    }\n    _setupAsAnchor() {\n      this._cleanupClick = this._ngZone.runOutsideAngular(() => this._renderer.listen(this._elementRef.nativeElement, 'click', event => {\n        if (this.disabled) {\n          event.preventDefault();\n          event.stopImmediatePropagation();\n        }\n      }));\n    }\n    static ɵfac = function MatButtonBase_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatButtonBase)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatButtonBase,\n      hostAttrs: [1, \"mat-mdc-button-base\"],\n      hostVars: 13,\n      hostBindings: function MatButtonBase_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"disabled\", ctx._getDisabledAttribute())(\"aria-disabled\", ctx._getAriaDisabled())(\"tabindex\", ctx._getTabIndex());\n          i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n          i0.ɵɵclassProp(\"mat-mdc-button-disabled\", ctx.disabled)(\"mat-mdc-button-disabled-interactive\", ctx.disabledInteractive)(\"mat-unthemed\", !ctx.color)(\"_mat-animation-noopable\", ctx._animationsDisabled);\n        }\n      },\n      inputs: {\n        color: \"color\",\n        disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        ariaDisabled: [2, \"aria-disabled\", \"ariaDisabled\", booleanAttribute],\n        disabledInteractive: [2, \"disabledInteractive\", \"disabledInteractive\", booleanAttribute],\n        tabIndex: [2, \"tabIndex\", \"tabIndex\", transformTabIndex],\n        _tabindex: [2, \"tabindex\", \"_tabindex\", transformTabIndex]\n      }\n    });\n  }\n  return MatButtonBase;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Material Design icon button component. This type of button displays a single interactive icon for\n * users to perform an action.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\nlet MatIconButton = /*#__PURE__*/(() => {\n  class MatIconButton extends MatButtonBase {\n    constructor() {\n      super();\n      this._rippleLoader.configureRipple(this._elementRef.nativeElement, {\n        centered: true\n      });\n    }\n    static ɵfac = function MatIconButton_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatIconButton)();\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatIconButton,\n      selectors: [[\"button\", \"mat-icon-button\", \"\"], [\"a\", \"mat-icon-button\", \"\"], [\"button\", \"matIconButton\", \"\"], [\"a\", \"matIconButton\", \"\"]],\n      hostAttrs: [1, \"mdc-icon-button\", \"mat-mdc-icon-button\"],\n      exportAs: [\"matButton\", \"matAnchor\"],\n      features: [i0.ɵɵInheritDefinitionFeature],\n      attrs: _c0,\n      ngContentSelectors: _c1,\n      decls: 4,\n      vars: 0,\n      consts: [[1, \"mat-mdc-button-persistent-ripple\", \"mdc-icon-button__ripple\"], [1, \"mat-focus-indicator\"], [1, \"mat-mdc-button-touch-target\"]],\n      template: function MatIconButton_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelement(0, \"span\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelement(2, \"span\", 1)(3, \"span\", 2);\n        }\n      },\n      styles: [\".mat-mdc-icon-button{-webkit-user-select:none;user-select:none;display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;text-decoration:none;cursor:pointer;z-index:0;overflow:visible;border-radius:var(--mat-icon-button-container-shape, var(--mat-sys-corner-full, 50%));flex-shrink:0;text-align:center;width:var(--mat-icon-button-state-layer-size, 40px);height:var(--mat-icon-button-state-layer-size, 40px);padding:calc(calc(var(--mat-icon-button-state-layer-size, 40px) - var(--mat-icon-button-icon-size, 24px)) / 2);font-size:var(--mat-icon-button-icon-size, 24px);color:var(--mat-icon-button-icon-color, var(--mat-sys-on-surface-variant));-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:\\\"\\\";opacity:0}.mat-mdc-icon-button .mdc-button__label,.mat-mdc-icon-button .mat-icon{z-index:1;position:relative}.mat-mdc-icon-button .mat-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:inherit}.mat-mdc-icon-button:focus>.mat-focus-indicator::before{content:\\\"\\\";border-radius:inherit}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color, color-mix(in srgb, var(--mat-sys-on-surface-variant) calc(var(--mat-sys-pressed-state-layer-opacity) * 100%), transparent))}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-icon-button:hover>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-icon-button.cdk-program-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused>.mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-icon-button:active>.mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity, var(--mat-sys-pressed-state-layer-opacity))}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;display:var(--mat-icon-button-touch-target-display, block);left:50%;width:48px;transform:translate(-50%, -50%)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mat-icon-button-disabled-icon-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button img,.mat-mdc-icon-button svg{width:var(--mat-icon-button-icon-size, 24px);height:var(--mat-icon-button-icon-size, 24px);vertical-align:baseline}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:var(--mat-icon-button-container-shape, var(--mat-sys-corner-full, 50%))}.mat-mdc-icon-button[hidden]{display:none}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}\\n\", \"@media(forced-colors: active){.mat-mdc-button:not(.mdc-button--outlined),.mat-mdc-unelevated-button:not(.mdc-button--outlined),.mat-mdc-raised-button:not(.mdc-button--outlined),.mat-mdc-outlined-button:not(.mdc-button--outlined),.mat-mdc-button-base.mat-tonal-button,.mat-mdc-icon-button.mat-mdc-icon-button,.mat-mdc-outlined-button .mdc-button__ripple{outline:solid 1px}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return MatIconButton;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n// tslint:disable:variable-name\n/**\n * Material Design icon button component for anchor elements. This button displays a single\n * interaction icon that allows users to navigate across different routes or pages.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\nconst MatIconAnchor = MatIconButton;\nexport { MatIconButton as M, MatButtonBase as a, MAT_BUTTON_CONFIG as b, MatIconAnchor as c };\n//# sourceMappingURL=icon-button-DxiIc1ex.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}