import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse, HttpResponse, HttpEventType } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, finalize, tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { LoadingService } from '../services/loading.service';
import { RateLimitService } from '../services/rate-limit.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(
    private authService: AuthService,
    private router: Router,
    private loadingService: LoadingService,
    private rateLimitService: RateLimitService
  ) {}

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Show loading indicator
    this.loadingService.show();

    // Add auth token if available
    const token = this.authService.getToken();
    if (token && !this.authService.isTokenExpired()) {
      request = request.clone({
        setHeaders: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        }
      });
    }

    // Add CSRF protection
    request = request.clone({
      setHeaders: {
        'X-CSRF-Token': this.generateCSRFToken()
      }
    });

    return next.handle(request).pipe(
      tap((event: HttpEvent<any>) => {
        // Handle successful responses and extract rate limit headers
        if (event.type === HttpEventType.Response) {
          const response = event as HttpResponse<any>;
          if (response.headers) {
            this.rateLimitService.handleRateLimitResponse(this.extractHeaders(response.headers));
          }
        }
      }),
      catchError((error: HttpErrorResponse) => {
        this.handleError(error);
        return throwError(() => error);
      }),
      finalize(() => {
        // Hide loading indicator
        this.loadingService.hide();
      })
    );
  }

  private handleError(error: HttpErrorResponse): void {
    console.log('🚨 HTTP Error intercepted:', error);
    console.log('🔍 Error details:', {
      status: error.status,
      message: error.message,
      url: error.url,
      statusText: error.statusText,
      error: error.error
    });
    
    // Enhanced rate limit detection for all error types (must be first)
    if (this.detectRateLimitError(error)) {
      console.warn('🚦 Rate limit detected via enhanced analysis - triggering popup');
      this.handleRateLimitError(error);
      return; // Don't continue with other error handling
    } else {
      console.log('❌ Rate limit NOT detected - continuing with normal error handling');
    }

    switch (error.status) {
      case 401:
        // Check if this is an account lockout vs. token expiry
        const errorMessage = error.error?.error?.message || error.error?.message || '';
        
        if (errorMessage.includes('temporarily locked') || 
            errorMessage.includes('multiple failed login attempts')) {
          // This is an account lockout - don't auto-logout, let the login component handle it
          console.log('Account lockout detected - allowing login component to handle');
          return;
        }
        
        // Regular unauthorized - token expired or invalid
        this.authService.logout();
        this.router.navigate(['/auth/login'], {
          queryParams: { message: 'Session expired. Please login again.' }
        });
        break;
      case 403:
        // Forbidden - insufficient permissions
        this.router.navigate(['/unauthorized']);
        break;
      case 429:
        // Too many requests - should already be handled by enhanced detection above
        console.warn('🚦 Direct 429 status - already handled by enhanced detection');
        break;
      case 0:
        // Network error - could be rate limit with CORS issues (handled by enhanced detection)
        console.error('🌐 Network error. Please check your connection.');
        break;
      default:
        console.error('❌ HTTP Error:', error);
    }
  }

  /**
   * Enhanced rate limit detection from various error patterns
   * Now specifically searches for "rate" in error messages to avoid false positives
   */
  private detectRateLimitError(error: HttpErrorResponse): boolean {
    // Direct 429 status
    if (error.status === 429) return true;
    
    // Check error message for rate limit indicators
    const errorMessage = error.message?.toLowerCase() || '';
    const errorText = error.error?.toString?.()?.toLowerCase() || '';
    const errorDetails = error.error?.error?.message?.toLowerCase() || '';
    const statusText = error.statusText?.toLowerCase() || '';
    const url = error.url?.toLowerCase() || '';
    
    // More specific rate limit indicators - must contain "rate" or very specific rate limit terms
    const rateLimitIndicators = [
      'too many requests',
      '(too many requests)', // Your specific console error format
      'rate limit',
      'ratelimit',
      'rate-limit',
      'rate exceeded',
      'quota exceeded',
      'throttled',
      'throttle'
    ];
    
    // Check if error contains specific rate limit indicators
    const hasRateLimitPattern = rateLimitIndicators.some(indicator => 
      errorMessage.includes(indicator) || 
      errorText.includes(indicator) || 
      errorDetails.includes(indicator) ||
      statusText.includes(indicator)
    );
    
    // Only check for specific rate-related network failures
    // Must have "rate" in the error message to be considered a rate limit
    const hasRateInMessage = errorMessage.includes('rate') || 
                            errorText.includes('rate') || 
                            errorDetails.includes('rate') ||
                            statusText.includes('rate');
    
    // Network failure that specifically mentions rate limiting
    const hasNetworkFailureWithRate = (error.status === 0 || errorMessage.includes('net::err_failed')) && hasRateInMessage;
    
    // Log detection for debugging
    if (hasRateLimitPattern || hasNetworkFailureWithRate) {
      console.log('🚦 Rate limit detected:', {
        status: error.status,
        message: errorMessage,
        text: errorText,
        details: errorDetails,
        statusText: statusText,
        url: url,
        rateLimitPattern: hasRateLimitPattern,
        networkFailureWithRate: hasNetworkFailureWithRate,
        hasRateInMessage: hasRateInMessage
      });
      return true;
    }
    
    console.log('❌ Rate limit NOT detected - error does not contain rate limit indicators:', {
      status: error.status,
      message: errorMessage.substring(0, 100) + '...',
      hasRateInMessage: hasRateInMessage
    });
    
    return false;
  }

  /**
   * Handle rate limit error with smart fallback data
   */
  private handleRateLimitError(error: HttpErrorResponse): void {
    console.log('🚦 Handling rate limit error:', error);
    
    // Try to extract headers if available
    let headers: {[key: string]: string} = {};
    
    if (error.headers) {
      headers = this.extractHeaders(error.headers);
    }
    
    // Check if we have actual rate limit headers from the server
    const hasRateLimitHeaders = headers['retry-after'] || 
                                headers['x-ratelimit-retryafter'] || 
                                headers['x-ratelimit-limit'] ||
                                headers['x-ratelimit-remaining'];
    
    // If no rate limit headers but we detected a rate limit, provide smart fallback values
    if (!hasRateLimitHeaders) {
      headers = this.generateSmartRateLimitHeaders(error.url || '');
      console.log('🔄 Using smart fallback rate limit headers:', headers);
    } else {
      console.log('✅ Using actual rate limit headers from server:', headers);
    }
    
    // Handle the rate limit response
    this.rateLimitService.handleRateLimitResponse(headers, error);
  }

  /**
   * Generate smart fallback rate limit headers based on URL patterns
   */
  private generateSmartRateLimitHeaders(url: string): {[key: string]: string} {
    const now = Math.floor(Date.now() / 1000);
    
    // Match backend configuration: RATE_LIMIT_WINDOW=1 minute, RATE_LIMIT_MAX=2
    let retryAfter = 60; // 1 minute to match backend
    let limit = 2; // Match backend RATE_LIMIT_MAX=2
    
    if (url.includes('/auth/login')) {
      retryAfter = 60; // 1 minute for login attempts (match backend)
      limit = 2;
    } else if (url.includes('/auth/register')) {
      retryAfter = 60; // 1 minute for registration
      limit = 2;
    } else if (url.includes('/auth/reset') || url.includes('/auth/forgot')) {
      retryAfter = 60; // 1 minute for password reset
      limit = 2;
    } else if (url.includes('/auth/verify')) {
      retryAfter = 60; // 1 minute for verification codes
      limit = 2;
    } else if (url.includes('/auth/')) {
      retryAfter = 60; // 1 minute for other auth endpoints
      limit = 2;
    }
    
    return {
      'retry-after': retryAfter.toString(),
      'x-ratelimit-limit': limit.toString(),
      'x-ratelimit-remaining': '0',
      'x-ratelimit-reset': (now + retryAfter).toString(),
      'x-ratelimit-totalrequests': limit.toString()
    };
  }

  private generateCSRFToken(): string {
    // Simple CSRF token generation
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * Extract headers from HttpHeaders object
   */
  private extractHeaders(httpHeaders: any): {[key: string]: string} {
    const headers: {[key: string]: string} = {};
    
    if (httpHeaders && httpHeaders.keys) {
      httpHeaders.keys().forEach((key: string) => {
        headers[key.toLowerCase()] = httpHeaders.get(key);
      });
    }
    
    return headers;
  }
}
