{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let AccountDeletionService = /*#__PURE__*/(() => {\n  class AccountDeletionService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = `${environment.apiUrl}/account`;\n    }\n    /**\n     * Request account deletion with preferences\n     */\n    requestAccountDeletion(preferences) {\n      const headers = this.getAuthHeaders();\n      return this.http.post(`${this.apiUrl}/request-deletion`, preferences, {\n        headers\n      });\n    }\n    /**\n     * Get current deletion status\n     */\n    getDeletionStatus() {\n      const headers = this.getAuthHeaders();\n      return this.http.get(`${this.apiUrl}/deletion-status`, {\n        headers\n      });\n    }\n    /**\n     * Cancel pending account deletion\n     */\n    cancelDeletion() {\n      const headers = this.getAuthHeaders();\n      return this.http.post(`${this.apiUrl}/cancel-deletion`, {}, {\n        headers\n      });\n    }\n    /**\n     * Confirm account deletion with token (public endpoint)\n     */\n    confirmDeletion(token) {\n      return this.http.post(`${this.apiUrl}/confirm-deletion`, {\n        token\n      });\n    }\n    /**\n     * Check for preserved data during signup (public endpoint)\n     */\n    checkPreservedData(email) {\n      return this.http.get(`${this.apiUrl}/check-preserved-data/${encodeURIComponent(email)}`);\n    }\n    /**\n     * Restore preserved data (public endpoint - used during signup)\n     */\n    restoreData(userId, email, restoreOptions) {\n      return this.http.post(`${this.apiUrl}/restore-data`, {\n        userId,\n        email,\n        ...restoreOptions\n      });\n    }\n    /**\n     * Permanently delete all preserved data (public endpoint)\n     */\n    deletePreservedData(email) {\n      return this.http.request('delete', `${this.apiUrl}/delete-preserved-data`, {\n        body: {\n          email\n        }\n      });\n    }\n    /**\n     * Export user data as downloadable file\n     */\n    exportUserData() {\n      const headers = this.getAuthHeaders();\n      return this.http.get(`${this.apiUrl}/export-data`, {\n        headers,\n        responseType: 'blob'\n      });\n    }\n    /**\n     * Request data export via email\n     */\n    requestDataExport() {\n      const headers = this.getAuthHeaders();\n      return this.http.post(`${this.apiUrl}/request-export`, {}, {\n        headers\n      });\n    }\n    /**\n     * Get authentication headers\n     */\n    getAuthHeaders() {\n      const token = localStorage.getItem('authToken');\n      return new HttpHeaders({\n        'Authorization': `Bearer ${token}`,\n        'Content-Type': 'application/json'\n      });\n    }\n    /**\n     * Validate deletion preferences\n     */\n    validateDeletionPreferences(preferences) {\n      const errors = [];\n      // Check retention period\n      if (preferences.customRetentionPeriod !== undefined) {\n        if (preferences.customRetentionPeriod < 1 || preferences.customRetentionPeriod > 365) {\n          errors.push('Retention period must be between 1 and 365 days');\n        }\n      }\n      // Warn if no data is being preserved\n      const hasAnyPreservation = preferences.preservePaymentData || preferences.preserveTransactionHistory || preferences.preserveProfileData || preferences.preserveSecurityLogs;\n      if (!hasAnyPreservation) {\n        // This is just a warning, not an error\n        console.warn('No data will be preserved - account deletion will be permanent');\n      }\n      return {\n        isValid: errors.length === 0,\n        errors\n      };\n    }\n    /**\n     * Calculate data expiry date\n     */\n    calculateExpiryDate(retentionPeriodDays = 30) {\n      const expiryDate = new Date();\n      expiryDate.setDate(expiryDate.getDate() + retentionPeriodDays);\n      return expiryDate;\n    }\n    /**\n     * Format preserved data summary for display\n     */\n    formatPreservedDataSummary(summary) {\n      const items = [];\n      if (summary.paymentRecords > 0) {\n        items.push(`${summary.paymentRecords} payment record${summary.paymentRecords === 1 ? '' : 's'}`);\n      }\n      if (summary.transactionHistory > 0) {\n        items.push(`${summary.transactionHistory} transaction${summary.transactionHistory === 1 ? '' : 's'}`);\n      }\n      if (summary.profileBackup) {\n        items.push('Profile backup');\n      }\n      if (summary.securityEvents > 0) {\n        items.push(`${summary.securityEvents} security event${summary.securityEvents === 1 ? '' : 's'}`);\n      }\n      if (summary.loginHistory > 0) {\n        items.push(`${summary.loginHistory} login record${summary.loginHistory === 1 ? '' : 's'}`);\n      }\n      return items;\n    }\n    /**\n     * Get recommended deletion preferences based on user activity\n     */\n    getRecommendedPreferences(user) {\n      // Basic recommendations - can be enhanced based on user data\n      return {\n        preservePaymentData: true,\n        // Usually recommended to preserve for legal/tax purposes\n        preserveTransactionHistory: true,\n        // Financial records are important\n        preserveProfileData: false,\n        // Profile data is usually not needed\n        preserveSecurityLogs: false,\n        // Security logs are usually not needed by users\n        customRetentionPeriod: 90,\n        // 3 months is a good default\n        reason: ''\n      };\n    }\n    static #_ = this.ɵfac = function AccountDeletionService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AccountDeletionService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AccountDeletionService,\n      factory: AccountDeletionService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AccountDeletionService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}