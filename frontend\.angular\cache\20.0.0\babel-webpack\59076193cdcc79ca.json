{"ast": null, "code": "import { Observable, throwError } from 'rxjs';\nimport { catchError } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nexport let PaymentService = /*#__PURE__*/(() => {\n  class PaymentService {\n    constructor(http, authService) {\n      this.http = http;\n      this.authService = authService;\n    }\n    createPaymentOrder(request) {\n      return this.http.post(`${environment.apiUrl}/payments/create-order`, request, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(catchError(this.handleError));\n    }\n    verifyPayment(verification) {\n      return this.http.post(`${environment.apiUrl}/payments/verify`, verification, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(catchError(this.handleError));\n    }\n    getPaymentStatus(orderId) {\n      return this.http.get(`${environment.apiUrl}/payments/status/${orderId}`, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(catchError(this.handleError));\n    }\n    getMyPayments() {\n      return this.http.get(`${environment.apiUrl}/payments/my-payments`, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(catchError(this.handleError));\n    }\n    refundPayment(request) {\n      return this.http.post(`${environment.apiUrl}/payments/refund`, request, {\n        headers: this.authService.getAuthHeaders()\n      }).pipe(catchError(this.handleError));\n    }\n    initiateRazorpayPayment(order) {\n      return new Promise((resolve, reject) => {\n        if (!window.Razorpay) {\n          reject(new Error('Razorpay SDK not loaded'));\n          return;\n        }\n        const currentUser = this.authService.currentUserValue;\n        if (!currentUser) {\n          reject(new Error('User not authenticated'));\n          return;\n        }\n        const options = {\n          key: order.key,\n          amount: order.amount * 100,\n          // Convert to smallest currency unit\n          currency: order.currency,\n          name: environment.appName,\n          description: 'Payment for services',\n          order_id: order.orderId,\n          handler: response => {\n            resolve(response);\n          },\n          prefill: {\n            name: `${currentUser.firstName} ${currentUser.lastName}`,\n            email: currentUser.email,\n            contact: currentUser.phone\n          },\n          theme: {\n            color: '#3f51b5'\n          },\n          modal: {\n            ondismiss: () => {\n              reject(new Error('Payment cancelled by user'));\n            }\n          }\n        };\n        const razorpayInstance = new window.Razorpay(options);\n        razorpayInstance.open();\n      });\n    }\n    processPayment(request) {\n      return new Observable(observer => {\n        // Step 1: Create order\n        this.createPaymentOrder(request).subscribe({\n          next: order => {\n            // Step 2: Open Razorpay checkout\n            this.initiateRazorpayPayment(order).then(razorpayResponse => {\n              // Step 3: Verify payment\n              const verification = {\n                orderId: razorpayResponse.razorpay_order_id,\n                paymentId: razorpayResponse.razorpay_payment_id,\n                signature: razorpayResponse.razorpay_signature\n              };\n              this.verifyPayment(verification).subscribe({\n                next: response => {\n                  observer.next(response);\n                  observer.complete();\n                },\n                error: error => {\n                  observer.error(error);\n                }\n              });\n            }).catch(error => {\n              observer.error(error);\n            });\n          },\n          error: error => {\n            observer.error(error);\n          }\n        });\n      });\n    }\n    formatCurrency(amount, currency) {\n      const formatter = new Intl.NumberFormat('en-IN', {\n        style: 'currency',\n        currency: currency,\n        minimumFractionDigits: 2\n      });\n      return formatter.format(amount);\n    }\n    getPaymentStatusColor(status) {\n      switch (status) {\n        case 'paid':\n          return 'success';\n        case 'pending':\n          return 'warning';\n        case 'failed':\n          return 'danger';\n        case 'cancelled':\n          return 'secondary';\n        case 'refunded':\n          return 'info';\n        default:\n          return 'secondary';\n      }\n    }\n    getStatusIcon(status) {\n      switch (status) {\n        case 'paid':\n          return 'check_circle';\n        case 'pending':\n          return 'schedule';\n        case 'failed':\n          return 'error';\n        case 'cancelled':\n          return 'cancel';\n        case 'refunded':\n          return 'undo';\n        default:\n          return 'help';\n      }\n    }\n    validateAmount(amount, currency) {\n      if (!amount || amount <= 0) {\n        return {\n          valid: false,\n          error: 'Amount must be greater than 0'\n        };\n      }\n      const minAmount = currency === 'INR' ? 1 : 0.5; // Minimum amounts\n      const maxAmount = currency === 'INR' ? 1000000 : 10000; // Maximum amounts\n      if (amount < minAmount) {\n        return {\n          valid: false,\n          error: `Minimum amount is ${this.formatCurrency(minAmount, currency)}`\n        };\n      }\n      if (amount > maxAmount) {\n        return {\n          valid: false,\n          error: `Maximum amount is ${this.formatCurrency(maxAmount, currency)}`\n        };\n      }\n      return {\n        valid: true\n      };\n    }\n    convertCurrency(amount, fromCurrency, toCurrency) {\n      // Simple conversion for demo - in production, use real exchange rates\n      if (fromCurrency === toCurrency) return amount;\n      const rates = {\n        'INR_TO_USD': 0.012,\n        'USD_TO_INR': 83.0\n      };\n      const rateKey = `${fromCurrency}_TO_${toCurrency}`;\n      return rates[rateKey] ? amount * rates[rateKey] : amount;\n    }\n    handleError(error) {\n      let errorMessage = 'Payment service error occurred';\n      if (error.error instanceof ErrorEvent) {\n        errorMessage = error.error.message;\n      } else {\n        errorMessage = error.error?.message || error.message || `Error Code: ${error.status}`;\n      }\n      console.error('Payment Service Error:', error);\n      return throwError(() => new Error(errorMessage));\n    }\n    static #_ = this.ɵfac = function PaymentService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PaymentService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PaymentService,\n      factory: PaymentService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return PaymentService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}