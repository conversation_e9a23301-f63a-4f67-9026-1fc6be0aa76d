{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Disable2FADialogComponent } from '../disable-2fa-dialog/disable-2fa-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"../../../services/oauth.service\";\nimport * as i4 from \"../../../services/two-factor.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/material/snack-bar\";\nimport * as i7 from \"@angular/material/dialog\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/button\";\nimport * as i12 from \"@angular/material/icon\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nfunction LoginComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"mat-icon\", 10);\n    i0.ɵɵtext(3, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h4\");\n    i0.ɵɵtext(5, \"Account Temporarily Locked\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\", 11);\n    i0.ɵɵtext(7, \" Your account has been locked due to multiple failed login attempts. Please wait 30 minutes or try one of these options to unlock immediately: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 12)(9, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_12_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToForgotPassword());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"lock_reset\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Reset Password \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function LoginComponent_div_12_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleOTPLogin());\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"sms\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16, \" Login with OTP \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"a\", 15)(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"support\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20, \" Contact Support \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction LoginComponent_form_13_mat_spinner_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 34);\n  }\n}\nfunction LoginComponent_form_13_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_13_button_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_13_button_33_Template_button_click_0_listener() {\n      const provider_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.loginWithOAuth(provider_r5.name));\n    });\n    i0.ɵɵelement(1, \"i\");\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const provider_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"border-color\", provider_r5.color);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(provider_r5.icon);\n    i0.ɵɵstyleProp(\"color\", provider_r5.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(provider_r5.displayName);\n  }\n}\nfunction LoginComponent_form_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 16);\n    i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_form_13_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(1, \"mat-form-field\", 17)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Email Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 18);\n    i0.ɵɵelementStart(5, \"mat-icon\", 19);\n    i0.ɵɵtext(6, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-error\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-form-field\", 17)(10, \"mat-label\");\n    i0.ɵɵtext(11, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 20);\n    i0.ɵɵelementStart(13, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_13_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.hidePassword = !ctx_r1.hidePassword);\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"mat-error\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"button\", 22);\n    i0.ɵɵtemplate(19, LoginComponent_form_13_mat_spinner_19_Template, 1, 0, \"mat-spinner\", 23)(20, LoginComponent_form_13_span_20_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 25)(22, \"span\");\n    i0.ɵɵtext(23, \"or\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_13_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleOTPLogin());\n    });\n    i0.ɵɵelementStart(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"sms\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(27, \" Login with OTP \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 27)(29, \"div\", 25)(30, \"span\");\n    i0.ɵɵtext(31, \"or continue with\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 28);\n    i0.ɵɵtemplate(33, LoginComponent_form_13_button_33_Template, 4, 8, \"button\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 30)(35, \"a\", 31);\n    i0.ɵɵtext(36, \"Forgot Password?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 32)(38, \"span\");\n    i0.ɵɵtext(39, \"Don't have an account? \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"a\", 33);\n    i0.ɵɵtext(41, \"Sign Up\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.loginForm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.loginForm, \"email\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"type\", ctx_r1.hidePassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.hidePassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.loginForm, \"password\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.oauthProviders);\n  }\n}\nfunction LoginComponent_form_14_mat_form_field_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 17)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Authentication Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 48);\n    i0.ɵɵelementStart(4, \"mat-icon\", 19);\n    i0.ɵɵtext(5, \"verified_user\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-error\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.twoFactorForm, \"twoFactorToken\"));\n  }\n}\nfunction LoginComponent_form_14_mat_form_field_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 17)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Recovery Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 49);\n    i0.ɵɵelementStart(4, \"mat-icon\", 19);\n    i0.ɵɵtext(5, \"restore\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-error\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.twoFactorForm, \"recoveryCode\"));\n  }\n}\nfunction LoginComponent_form_14_mat_spinner_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 34);\n  }\n}\nfunction LoginComponent_form_14_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.useRecoveryCode ? \"Use Recovery Code\" : \"Verify & Sign In\");\n  }\n}\nfunction LoginComponent_form_14_button_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_button_27_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openDisable2FAFromRecovery());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" All recovery codes used? \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 16);\n    i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_form_14_Template_form_ngSubmit_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTwoFactorSubmit());\n    });\n    i0.ɵɵelementStart(1, \"div\", 36)(2, \"mat-icon\", 37);\n    i0.ɵɵtext(3, \"security\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Two-Factor Authentication\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, LoginComponent_form_14_mat_form_field_8_Template, 8, 1, \"mat-form-field\", 38)(9, LoginComponent_form_14_mat_form_field_9_Template, 8, 1, \"mat-form-field\", 38);\n    i0.ɵɵelementStart(10, \"button\", 22);\n    i0.ɵɵtemplate(11, LoginComponent_form_14_mat_spinner_11_Template, 1, 0, \"mat-spinner\", 23)(12, LoginComponent_form_14_span_12_Template, 2, 1, \"span\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 39)(14, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleRecoveryCode());\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 41)(19, \"div\", 42)(20, \"span\");\n    i0.ɵɵtext(21, \"Need help with 2FA?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 43)(23, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openDisable2FADialog());\n    });\n    i0.ɵɵelementStart(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26, \" Can't access your device? \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, LoginComponent_form_14_button_27_Template, 4, 0, \"button\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"p\", 46);\n    i0.ɵɵtext(29, \" Lost access to your authenticator or used all recovery codes? We can send you a secure email to disable 2FA. \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_14_Template_button_click_30_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.backToLogin());\n    });\n    i0.ɵɵelementStart(31, \"mat-icon\");\n    i0.ɵɵtext(32, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(33, \" Back to Login \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.twoFactorForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.useRecoveryCode ? \"Enter one of your recovery codes\" : \"Enter the 6-digit code from your authenticator app\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.useRecoveryCode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.useRecoveryCode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.useRecoveryCode ? \"smartphone\" : \"restore\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.useRecoveryCode ? \"Use Authenticator App\" : \"Use Recovery Code\", \" \");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.useRecoveryCode);\n  }\n}\nfunction LoginComponent_form_15_button_16_mat_spinner_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 34);\n  }\n}\nfunction LoginComponent_form_15_button_16_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Send OTP\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_15_button_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_15_button_16_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.sendOTP());\n    });\n    i0.ɵɵtemplate(1, LoginComponent_form_15_button_16_mat_spinner_1_Template, 1, 0, \"mat-spinner\", 23)(2, LoginComponent_form_15_button_16_span_2_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction LoginComponent_form_15_div_17_mat_spinner_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 34);\n  }\n}\nfunction LoginComponent_form_15_div_17_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Verify & Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_form_15_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"mat-form-field\", 17)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Enter OTP\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"input\", 55);\n    i0.ɵɵelementStart(5, \"mat-icon\", 19);\n    i0.ɵɵtext(6, \"lock\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-error\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_15_div_17_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.loginWithOTP());\n    });\n    i0.ɵɵtemplate(10, LoginComponent_form_15_div_17_mat_spinner_10_Template, 1, 0, \"mat-spinner\", 23)(11, LoginComponent_form_15_div_17_span_11_Template, 2, 0, \"span\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_15_div_17_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.sendOTP());\n    });\n    i0.ɵɵelementStart(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Resend OTP \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.otpForm, \"code\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction LoginComponent_form_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 51)(1, \"div\", 36)(2, \"mat-icon\", 37);\n    i0.ɵɵtext(3, \"sms\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Login with OTP\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Enter your email or phone number to receive a one-time password\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-form-field\", 17)(9, \"mat-label\");\n    i0.ɵɵtext(10, \"Email or Phone Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"input\", 52);\n    i0.ɵɵelementStart(12, \"mat-icon\", 19);\n    i0.ɵɵtext(13, \"contact_mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-error\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, LoginComponent_form_15_button_16_Template, 3, 3, \"button\", 53)(17, LoginComponent_form_15_div_17_Template, 16, 4, \"div\", 24);\n    i0.ɵɵelementStart(18, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function LoginComponent_form_15_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleOTPLogin());\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Back to Login \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.otpForm);\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r1.getFieldError(ctx_r1.otpForm, \"identifier\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.otpSent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.otpSent);\n  }\n}\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(formBuilder, authService, oauthService, twoFactorService, router, route, snackBar, dialog) {\n      this.formBuilder = formBuilder;\n      this.authService = authService;\n      this.oauthService = oauthService;\n      this.twoFactorService = twoFactorService;\n      this.router = router;\n      this.route = route;\n      this.snackBar = snackBar;\n      this.dialog = dialog;\n      this.loading = false;\n      this.hidePassword = true;\n      this.showOTPLogin = false;\n      this.showTwoFactor = false;\n      this.otpSent = false;\n      this.returnUrl = '';\n      this.oauthProviders = [];\n      this.useRecoveryCode = false;\n      this.accountLocked = false;\n      this.loginForm = this.formBuilder.group({\n        email: ['', [Validators.required, Validators.email]],\n        password: ['', [Validators.required, Validators.minLength(8)]]\n      });\n      this.otpForm = this.formBuilder.group({\n        identifier: ['', [Validators.required]],\n        code: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n      });\n      this.twoFactorForm = this.formBuilder.group({\n        twoFactorToken: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]],\n        recoveryCode: ['', [Validators.required]]\n      });\n    }\n    ngOnInit() {\n      this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n      this.oauthProviders = this.oauthService.getAvailableProviders();\n      if (this.authService.isAuthenticated) {\n        this.router.navigate([this.returnUrl]);\n      }\n      const message = this.route.snapshot.queryParams['message'];\n      if (message) {\n        this.snackBar.open(message, 'Close', {\n          duration: 5000\n        });\n      }\n      // Handle OAuth callback\n      const code = this.route.snapshot.queryParams['code'];\n      const state = this.route.snapshot.queryParams['state'];\n      if (code) {\n        this.handleOAuthCallback(code, state);\n      }\n    }\n    onSubmit() {\n      if (this.loginForm.invalid) {\n        this.markFormGroupTouched(this.loginForm);\n        return;\n      }\n      this.loading = true;\n      const credentials = this.loginForm.value;\n      this.authService.login(credentials).subscribe({\n        next: response => {\n          if (response.requiresTwoFactor) {\n            this.showTwoFactor = true;\n            this.snackBar.open('Please enter your two-factor authentication code', 'Close', {\n              duration: 5000\n            });\n          } else {\n            this.snackBar.open('Login successful!', 'Close', {\n              duration: 3000\n            });\n            this.router.navigate([this.returnUrl]);\n          }\n          this.loading = false;\n        },\n        error: error => {\n          this.handleLoginError(error);\n          this.loading = false;\n        }\n      });\n    }\n    onTwoFactorSubmit() {\n      // Check if the appropriate field is filled based on mode\n      const twoFactorToken = this.twoFactorForm.value.twoFactorToken;\n      const recoveryCode = this.twoFactorForm.value.recoveryCode;\n      if (this.useRecoveryCode && !recoveryCode) {\n        this.snackBar.open('Please enter a recovery code', 'Close', {\n          duration: 3000\n        });\n        return;\n      }\n      if (!this.useRecoveryCode && !twoFactorToken) {\n        this.snackBar.open('Please enter the authentication code', 'Close', {\n          duration: 3000\n        });\n        return;\n      }\n      this.loading = true;\n      const credentials = {\n        ...this.loginForm.value,\n        twoFactorToken: this.useRecoveryCode ? undefined : twoFactorToken,\n        recoveryCode: this.useRecoveryCode ? recoveryCode : undefined\n      };\n      this.authService.login(credentials).subscribe({\n        next: response => {\n          this.snackBar.open('Login successful!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate([this.returnUrl]);\n          this.loading = false;\n        },\n        error: error => {\n          this.handleLoginError(error);\n          this.loading = false;\n        }\n      });\n    }\n    sendOTP() {\n      const identifier = this.otpForm.get('identifier')?.value;\n      if (!identifier) {\n        this.snackBar.open('Please enter email or phone number', 'Close', {\n          duration: 3000\n        });\n        return;\n      }\n      this.loading = true;\n      const request = {\n        identifier,\n        type: 'login'\n      };\n      this.authService.sendOTP(request).subscribe({\n        next: () => {\n          this.otpSent = true;\n          this.snackBar.open('OTP sent successfully!', 'Close', {\n            duration: 3000\n          });\n          this.loading = false;\n        },\n        error: error => {\n          this.snackBar.open(error.message || 'Failed to send OTP', 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n        }\n      });\n    }\n    loginWithOTP() {\n      if (this.otpForm.invalid) {\n        this.markFormGroupTouched(this.otpForm);\n        return;\n      }\n      this.loading = true;\n      const {\n        identifier,\n        code\n      } = this.otpForm.value;\n      this.authService.loginWithOTP(identifier, code).subscribe({\n        next: () => {\n          this.snackBar.open('Login successful!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate([this.returnUrl]);\n          this.loading = false;\n        },\n        error: error => {\n          this.snackBar.open(error.message || 'OTP login failed', 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n        }\n      });\n    }\n    toggleOTPLogin() {\n      this.showOTPLogin = !this.showOTPLogin;\n      this.showTwoFactor = false;\n      this.otpSent = false;\n      this.otpForm.reset();\n      this.accountLocked = false; // Reset lockout state when switching to OTP\n    }\n    backToLogin() {\n      this.showTwoFactor = false;\n      this.showOTPLogin = false;\n      this.otpSent = false;\n    }\n    toggleRecoveryCode() {\n      this.useRecoveryCode = !this.useRecoveryCode;\n      // Update form validators based on the mode\n      const twoFactorTokenControl = this.twoFactorForm.get('twoFactorToken');\n      const recoveryCodeControl = this.twoFactorForm.get('recoveryCode');\n      if (this.useRecoveryCode) {\n        // Using recovery code - remove 2FA token requirement\n        twoFactorTokenControl?.clearValidators();\n        recoveryCodeControl?.setValidators([Validators.required]);\n      } else {\n        // Using 2FA token - remove recovery code requirement\n        twoFactorTokenControl?.setValidators([Validators.required, Validators.pattern(/^\\d{6}$/)]);\n        recoveryCodeControl?.clearValidators();\n      }\n      // Update validity\n      twoFactorTokenControl?.updateValueAndValidity();\n      recoveryCodeControl?.updateValueAndValidity();\n      // Clear the values\n      twoFactorTokenControl?.setValue('');\n      recoveryCodeControl?.setValue('');\n    }\n    // OAuth methods\n    loginWithOAuth(provider) {\n      this.loading = true;\n      this.oauthService.initiateOAuthLogin(provider);\n    }\n    handleOAuthCallback(code, state) {\n      this.loading = true;\n      this.oauthService.handleOAuthCallback(code, state).subscribe({\n        next: response => {\n          this.snackBar.open('OAuth login successful!', 'Close', {\n            duration: 3000\n          });\n          this.router.navigate([this.returnUrl]);\n          this.loading = false;\n        },\n        error: error => {\n          this.snackBar.open(error.message || 'OAuth login failed', 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n          // Remove OAuth parameters from URL\n          this.router.navigate([], {\n            relativeTo: this.route,\n            queryParams: {},\n            replaceUrl: true\n          });\n        }\n      });\n    }\n    getFieldError(form, fieldName) {\n      const field = form.get(fieldName);\n      if (field?.errors && field.touched) {\n        if (field.errors['required']) return `${fieldName} is required`;\n        if (field.errors['email']) return 'Please enter a valid email';\n        if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n        if (field.errors['pattern']) return 'Please enter a valid format';\n      }\n      return '';\n    }\n    markFormGroupTouched(formGroup) {\n      Object.keys(formGroup.controls).forEach(key => {\n        const control = formGroup.get(key);\n        control?.markAsTouched();\n      });\n    }\n    handleLoginError(error) {\n      // Extract error message from different possible error structures\n      let errorMessage = 'Login failed';\n      if (error?.message) {\n        errorMessage = error.message;\n      } else if (error?.error?.error?.message) {\n        errorMessage = error.error.error.message;\n      } else if (error?.error?.message) {\n        errorMessage = error.error.message;\n      }\n      console.log('Login Error Details:', {\n        error,\n        extractedMessage: errorMessage\n      });\n      // Check for email verification error\n      if (errorMessage.includes('verify your email') || errorMessage.includes('email verification') || errorMessage.includes('unverified email')) {\n        // Show email verification specific message with resend option\n        const snackBarRef = this.snackBar.open('Please verify your email before logging in. Check your inbox for a verification link.', 'Resend Email', {\n          duration: 10000,\n          panelClass: ['warning-snackbar']\n        });\n        snackBarRef.onAction().subscribe(() => {\n          this.resendVerificationEmail();\n        });\n        return;\n      }\n      // Check if it's an account lockout error\n      if (errorMessage.includes('temporarily locked') || errorMessage.includes('multiple failed login attempts')) {\n        this.accountLocked = true;\n        console.log('Account lockout detected, showing lockout notice');\n        // Show a more detailed error message for account lockout\n        this.snackBar.open(errorMessage, 'Close', {\n          duration: 15000,\n          // Longer duration for important message\n          panelClass: ['error-snackbar']\n        });\n      } else {\n        this.accountLocked = false;\n        this.snackBar.open(errorMessage, 'Close', {\n          duration: 5000\n        });\n      }\n    }\n    resendVerificationEmail() {\n      const email = this.loginForm.get('email')?.value;\n      if (!email) {\n        this.snackBar.open('Please enter your email address', 'Close', {\n          duration: 3000\n        });\n        return;\n      }\n      this.authService.resendVerification(email).subscribe({\n        next: response => {\n          this.snackBar.open('Verification email sent! Please check your inbox.', 'Close', {\n            duration: 5000\n          });\n        },\n        error: error => {\n          this.snackBar.open('Failed to send verification email. Please try again.', 'Close', {\n            duration: 5000\n          });\n        }\n      });\n    }\n    // Helper method to navigate to forgot password\n    goToForgotPassword() {\n      this.router.navigate(['/auth/forgot-password']);\n    }\n    // 2FA Disable Methods\n    openDisable2FADialog() {\n      const email = this.loginForm.get('email')?.value;\n      if (!email) {\n        this.snackBar.open('Please enter your email address first', 'Close', {\n          duration: 3000\n        });\n        return;\n      }\n      const dialogRef = this.dialog.open(Disable2FADialogComponent, {\n        width: '500px',\n        disableClose: true,\n        data: {\n          email: email,\n          allCodesUsed: false,\n          // We don't know this yet in login screen\n          source: 'login'\n        }\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result?.success) {\n          console.log('2FA disable request sent successfully');\n        }\n      });\n    }\n    openDisable2FAFromRecovery() {\n      const email = this.loginForm.get('email')?.value;\n      if (!email) {\n        this.snackBar.open('Please enter your email address first', 'Close', {\n          duration: 3000\n        });\n        return;\n      }\n      const dialogRef = this.dialog.open(Disable2FADialogComponent, {\n        width: '500px',\n        disableClose: true,\n        data: {\n          email: email,\n          allCodesUsed: true,\n          // Assuming codes are exhausted if they're using this option\n          source: 'recovery'\n        }\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result?.success) {\n          console.log('2FA disable request sent successfully from recovery');\n        }\n      });\n    }\n    static #_ = this.ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.OAuthService), i0.ɵɵdirectiveInject(i4.TwoFactorService), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i6.MatSnackBar), i0.ɵɵdirectiveInject(i7.MatDialog));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      standalone: false,\n      decls: 16,\n      vars: 4,\n      consts: [[1, \"auth-container\"], [1, \"auth-card\", \"fade-in\"], [1, \"auth-header\"], [1, \"security-badge\"], [1, \"auth-content\"], [\"class\", \"alert alert-warning lockout-notice\", \"role\", \"alert\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\", 4, \"ngIf\"], [3, \"formGroup\", 4, \"ngIf\"], [\"role\", \"alert\", 1, \"alert\", \"alert-warning\", \"lockout-notice\"], [1, \"lockout-header\"], [1, \"warning-icon\"], [1, \"mb-3\"], [1, \"unlock-options\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"unlock-btn\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"accent\", 1, \"unlock-btn\", 3, \"click\"], [\"href\", \"mailto:<EMAIL>\", \"mat-stroked-button\", \"\", 1, \"unlock-btn\"], [3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"autocomplete\", \"email\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"submit-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"divider\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"w-100\", 3, \"click\"], [1, \"oauth-section\", \"mt-3\"], [1, \"oauth-buttons\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", \"class\", \"oauth-button\", 3, \"border-color\", \"disabled\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-center\", \"mt-3\"], [\"routerLink\", \"/auth/forgot-password\", 1, \"text-primary\"], [1, \"text-center\", \"mt-2\"], [\"routerLink\", \"/auth/register\", 1, \"text-primary\"], [\"diameter\", \"20\"], [\"mat-stroked-button\", \"\", \"type\", \"button\", 1, \"oauth-button\", 3, \"click\", \"disabled\"], [1, \"text-center\", \"mb-3\"], [\"color\", \"primary\", 2, \"font-size\", \"48px\", \"width\", \"48px\", \"height\", \"48px\"], [\"class\", \"form-field\", \"appearance\", \"outline\", 4, \"ngIf\"], [1, \"recovery-options\", \"mt-3\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"w-100\", 3, \"click\"], [1, \"twofa-help-section\", \"mt-3\"], [1, \"help-divider\"], [1, \"help-options\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"help-button\", 3, \"click\"], [\"mat-button\", \"\", \"type\", \"button\", \"class\", \"help-button warn-button\", 3, \"click\", 4, \"ngIf\"], [1, \"help-text\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"w-100\", \"mt-2\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"twoFactorToken\", \"placeholder\", \"000000\", \"maxlength\", \"6\", \"autocomplete\", \"one-time-code\"], [\"matInput\", \"\", \"formControlName\", \"recoveryCode\", \"placeholder\", \"abcd-efgh-ijkl\", \"autocomplete\", \"one-time-code\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"help-button\", \"warn-button\", 3, \"click\"], [3, \"formGroup\"], [\"matInput\", \"\", \"formControlName\", \"identifier\", \"placeholder\", \"<EMAIL> or +1234567890\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"type\", \"button\", \"class\", \"submit-button\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", \"type\", \"button\", 1, \"submit-button\", 3, \"click\", \"disabled\"], [\"matInput\", \"\", \"formControlName\", \"code\", \"placeholder\", \"000000\", \"maxlength\", \"6\", \"autocomplete\", \"one-time-code\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"button\", 1, \"submit-button\", 3, \"click\", \"disabled\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\");\n          i0.ɵɵtext(4, \"Welcome Back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\");\n          i0.ɵɵtext(6, \"Sign in to your secure account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 3)(8, \"mat-icon\");\n          i0.ɵɵtext(9, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(10, \" Secure Login \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 4);\n          i0.ɵɵtemplate(12, LoginComponent_div_12_Template, 21, 0, \"div\", 5)(13, LoginComponent_form_13_Template, 42, 9, \"form\", 6)(14, LoginComponent_form_14_Template, 34, 10, \"form\", 6)(15, LoginComponent_form_15_Template, 22, 4, \"form\", 7);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.accountLocked);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.showOTPLogin && !ctx.showTwoFactor);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showTwoFactor);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showOTPLogin);\n        }\n      },\n      dependencies: [i8.NgForOf, i8.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MaxLengthValidator, i1.FormGroupDirective, i1.FormControlName, i5.RouterLink, i9.MatFormField, i9.MatLabel, i9.MatError, i9.MatSuffix, i10.MatInput, i11.MatButton, i11.MatIconButton, i12.MatIcon, i13.MatProgressSpinner],\n      styles: [\".w-100[_ngcontent-%COMP%]{width:100%}.mt-2[_ngcontent-%COMP%]{margin-top:.5rem}.mt-3[_ngcontent-%COMP%]{margin-top:1rem}.mb-3[_ngcontent-%COMP%]{margin-bottom:1rem}.text-center[_ngcontent-%COMP%]{text-align:center}.text-primary[_ngcontent-%COMP%]{color:#3f51b5;text-decoration:none}.text-primary[_ngcontent-%COMP%]:hover{text-decoration:underline}.oauth-section[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{position:relative;text-align:center;margin:1.5rem 0}.oauth-section[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:50%;left:0;right:0;height:1px;background:#e0e0e0}.oauth-section[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{background:#fff;padding:0 1rem;color:#666;font-size:.875rem}.oauth-buttons[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.75rem}.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:.75rem;padding:.75rem 1rem;width:100%;border-radius:8px;transition:all .2s ease}.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1.25rem}.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500}.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #0000001a}.oauth-buttons[_ngcontent-%COMP%]   .oauth-button[_ngcontent-%COMP%]:disabled{opacity:.6;cursor:not-allowed;transform:none;box-shadow:none}.lockout-notice[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff3cd,#ffeaa7);border:2px solid #ffc107;border-radius:12px;padding:1.5rem;margin-bottom:2rem;box-shadow:0 4px 12px #ffc10733}.lockout-notice[_ngcontent-%COMP%]   .lockout-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem;margin-bottom:1rem}.lockout-notice[_ngcontent-%COMP%]   .lockout-header[_ngcontent-%COMP%]   .warning-icon[_ngcontent-%COMP%]{color:#ff9800;font-size:2rem;width:2rem;height:2rem}.lockout-notice[_ngcontent-%COMP%]   .lockout-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0;color:#e65100;font-weight:600}.lockout-notice[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#8f5000;line-height:1.5;margin-bottom:1rem}.lockout-notice[_ngcontent-%COMP%]   .unlock-options[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.75rem}@media (min-width: 576px){.lockout-notice[_ngcontent-%COMP%]   .unlock-options[_ngcontent-%COMP%]{flex-direction:row;justify-content:space-between}}.lockout-notice[_ngcontent-%COMP%]   .unlock-options[_ngcontent-%COMP%]   .unlock-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:.5rem;padding:.75rem 1rem;font-size:.875rem;font-weight:500;border-radius:8px;transition:all .2s ease;text-decoration:none}.lockout-notice[_ngcontent-%COMP%]   .unlock-options[_ngcontent-%COMP%]   .unlock-btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 8px #0000001a}.lockout-notice[_ngcontent-%COMP%]   .unlock-options[_ngcontent-%COMP%]   .unlock-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1.125rem;width:1.125rem;height:1.125rem}  .error-snackbar{background-color:#f44336!important;color:#fff!important}  .error-snackbar .mat-simple-snackbar-action{color:#ffcdd2!important}.twofa-help-section[_ngcontent-%COMP%]{margin-top:1.5rem;padding:1rem;background:#f8f9fa;border-radius:8px;border:1px solid #e9ecef}.twofa-help-section[_ngcontent-%COMP%]   .help-divider[_ngcontent-%COMP%]{position:relative;text-align:center;margin-bottom:1rem}.twofa-help-section[_ngcontent-%COMP%]   .help-divider[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:50%;left:0;right:0;height:1px;background:#dee2e6}.twofa-help-section[_ngcontent-%COMP%]   .help-divider[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{background:#f8f9fa;padding:0 .75rem;color:#6c757d;font-size:.875rem;font-weight:500}.twofa-help-section[_ngcontent-%COMP%]   .help-options[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.5rem;margin-bottom:1rem}@media (min-width: 480px){.twofa-help-section[_ngcontent-%COMP%]   .help-options[_ngcontent-%COMP%]{flex-direction:row;justify-content:space-between}}.twofa-help-section[_ngcontent-%COMP%]   .help-button[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:.5rem;padding:.5rem 1rem;font-size:.875rem;border-radius:6px;transition:all .2s ease;background:#fff;border:1px solid #dee2e6;color:#495057}.twofa-help-section[_ngcontent-%COMP%]   .help-button[_ngcontent-%COMP%]:hover{background:#e9ecef;transform:translateY(-1px);box-shadow:0 2px 4px #0000001a}.twofa-help-section[_ngcontent-%COMP%]   .help-button.warn-button[_ngcontent-%COMP%]{border-color:#ffc107;color:#856404;background:#fff3cd}.twofa-help-section[_ngcontent-%COMP%]   .help-button.warn-button[_ngcontent-%COMP%]:hover{background:#ffeaa7;border-color:#ffb302}.twofa-help-section[_ngcontent-%COMP%]   .help-button.warn-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#ff9800}.twofa-help-section[_ngcontent-%COMP%]   .help-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:1rem;width:1rem;height:1rem}.twofa-help-section[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]{font-size:.8125rem;color:#6c757d;text-align:center;line-height:1.4;margin:0;font-style:italic}\"]\n    });\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}