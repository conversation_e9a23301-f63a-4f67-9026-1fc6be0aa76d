#!/usr/bin/env python3
"""
Create User with Email Verification Script
Creates a user account and sends verification email

Usage:
    python create_verified_user_updated.py
    
This script creates a user with email verification flow for realistic testing.
It handles existing users by updating their email to free constraints.
"""

import os
import sys
from datetime import datetime, timezone
import secrets
import string
import time
import requests
import json

# Set up the Masonite environment
from wsgi import application
from masonite.environment import LoadEnvironment
LoadEnvironment()

# Import after environment setup
from app.models.User import User
from app.models.SecurityEvent import SecurityEvent, SecurityEventType
from app.models.AccountDeletionRecord import AccountDeletionRecord
from app.mailables.EmailVerification import EmailVerification
from masonite.mail import Mail
from masonite.environment import env


def generate_secure_token(length=50):
    """Generate a secure random token"""
    alphabet = string.ascii_letters + string.digits + '-_'
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def delete_user_by_email(email):
    """
    Delete a user by email if they exist, handling constraints properly
    
    Args:
        email (str): Email address of user to delete
    
    Returns:
        bool: True if user was handled, False if not found
    """
    try:
        print(f"🔍 Checking for existing user with email: {email}")
        
        # Check for active users
        existing_user = User.where('email', email).first()
        if existing_user:
            user_id = existing_user.id
            print(f"👤 Found active user: {email} (ID: {user_id})")
            
            # Update email to free the constraint for new user
            timestamp = int(time.time())
            new_email = f"deleted_{timestamp}_{user_id}@deleted.local"
            
            try:
                existing_user.email = new_email
                existing_user.is_active = False
                existing_user.save()
                print(f"🔄 Updated email to: {new_email}")
                
                # Now delete the user (soft delete)
                existing_user.delete()
                print(f"🗑️  Soft deleted user: {user_id}")
                
            except Exception as e:
                print(f"⚠️  Could not update user {user_id}: {e}")
                # Alternative: directly delete and catch constraint error
                try:
                    existing_user.delete()
                    print(f"🗑️  Soft deleted user without email change: {user_id}")
                except Exception as e2:
                    print(f"⚠️  Could not delete user either: {e2}")
        
        # Also check for soft-deleted users with the same email
        if hasattr(User, 'with_trashed'):
            try:
                soft_deleted_users = User.with_trashed().where('email', email).get()
                for user in soft_deleted_users:
                    if hasattr(user, 'deleted_at') and user.deleted_at is not None:  # Confirm it's soft deleted
                        user_id = user.id
                        timestamp = int(time.time())
                        new_email = f"deleted_{timestamp}_{user_id}@deleted.local"
                        
                        try:
                            user.email = new_email
                            user.save()
                            print(f"🔄 Updated soft-deleted user {user_id} email to: {new_email}")
                        except Exception as e:
                            print(f"⚠️  Could not update soft-deleted user {user_id}: {e}")
            except Exception as e:
                print(f"ℹ️  Could not check soft-deleted users: {e}")
        
        # Check for any deletion records
        try:
            deletion_records = AccountDeletionRecord.where('email', email).get()
            for record in deletion_records:
                try:
                    record.delete()
                    print(f"🗑️  Removed deletion record for: {email}")
                except Exception as e:
                    print(f"⚠️  Could not remove deletion record: {e}")
        except Exception as e:
            print(f"ℹ️  Could not check deletion records: {e}")
        
        print(f"✅ Email {email} is now available for new user creation")
        return True
        
    except Exception as e:
        print(f"❌ Error handling existing user {email}: {e}")
        import traceback
        print(f"❌ Full traceback: {traceback.format_exc()}")
        return False


def create_user_with_verification(email, password, first_name="Test", last_name="User", phone=None):
    """
    Create a user and send verification email
    
    Args:
        email (str): User's email address
        password (str): User's password
        first_name (str): User's first name
        last_name (str): User's last name
        phone (str): User's phone number (optional)
    
    Returns:
        User: Created user object or None if failed
    """
    try:
        print(f"🚀 Creating user with email verification: {email}")
        
        # Delete/handle existing user first
        delete_user_by_email(email)
        
        # Generate API token
        api_token = generate_secure_token()
        
        # Create new user with hashed password
        user = User()
        user.email = email
        user.set_password(password)  # This will hash the password
        user.first_name = first_name
        user.last_name = last_name
        user.name = f"{first_name} {last_name}"  # Combined name field
        user.phone = phone
        user.api_token = api_token
        user.email_verified_at = None  # Not verified yet
        user.is_active = True
        user.last_login_at = None
        user.login_attempts = 0
        user.locked_until = None
        user.two_factor_enabled = False
        user.two_factor_secret = None
        user.oauth_providers = None
        user.roles = 'user'
        user.save()
        
        print(f"✅ User created successfully!")
        print(f"   📧 Email: {user.email}")
        print(f"   🆔 User ID: {user.id}")
        print(f"   👤 Name: {user.name}")
        print(f"   🔑 API Token: {user.api_token[:20]}...")
        print(f"   ❌ Email Verified: Not yet (verification email will be sent)")
        
        # Generate email verification token
        verification_token = user.generate_email_verification_token()
        print(f"   🎫 Verification Token: {verification_token[:20]}...")        # Send verification email (skip for now due to dependency injection complexity in scripts)
        verification_url = f"{env('FRONTEND_URL', 'http://localhost:4200')}/auth/verify-email?token={verification_token}"
        
        print(f"   📧 Email sending skipped (use URL below for manual verification)")
        print(f"   🌐 Verification URL: {verification_url}")
        print(f"   🔗 Copy this URL to your browser to verify the account")
        print(f"   💡 Token for API calls: {verification_token}")
        
        # Note about manual email sending
        print(f"   📝 To send verification email manually:")
        print(f"      - Use the EmailVerification mailable with token: {verification_token}")
        print(f"      - Or copy the URL above to verify directly")
        
        # Log security event
        try:
            SecurityEvent.log_event(
                event_type=SecurityEventType.ACCOUNT_CREATED.value,
                user_id=user.id,
                message=f"Account created for {email} - verification email sent",
                event_data={
                    'email': email,
                    'verification_required': True,
                    'created_via': 'admin_script'
                }
            )
            print(f"   📝 Security event logged")
        except Exception as e:
            print(f"   ⚠️  Could not log security event: {e}")
        
        return user
        
    except Exception as e:
        print(f"❌ Error creating user: {e}")
        import traceback
        print(f"❌ Full traceback: {traceback.format_exc()}")
        return None


def main():
    """Main function to create user with email verification"""
    print("=" * 70)
    print("🚀 Masonite User Creation with Email Verification Script")
    print("=" * 70)
    
    # Target user to create
    target_user = {
        'email': '<EMAIL>',
        'password': 'SecurePass123!',
        'first_name': 'Jitesh',
        'last_name': 'Ahuja',
        'phone': '+**********'
    }
    
    print(f"🎯 Target user: {target_user['email']}")
    print("-" * 50)
    
    # Create user with verification
    user = create_user_with_verification(**target_user)
    
    if user:
        print("\n🎉 Script completed successfully!")
        print(f"✅ User created: {user.email} (ID: {user.id})")
        print("\n📧 Next Steps:")
        print("1. Check the user's email inbox for verification email")
        print("2. Click the verification link to verify the account")
        print("3. User can then log in with verified email")
        
        print(f"\n🔑 Login Credentials:")
        print(f"   Email: {target_user['email']}")
        print(f"   Password: {target_user['password']}")
        
        print(f"\n🛠️ Manual Verification (if email fails):")
        print(f"   Use the verification token shown above with:")
        print(f"   POST /api/auth/verify-email")
        print(f"   Body: {{'token': 'VERIFICATION_TOKEN'}}")
        
        print(f"\n🔗 Direct Verification URL:")
        print(f"   Copy and paste the verification URL shown above into your browser")
        
    else:
        print("\n❌ Script failed!")
        print("Please check the error messages above.")
    
    print("\n💡 The user will need to verify their email before they can log in.")
    print("📧 Check email inbox for verification link!")
    print("\n" + "=" * 70)


if __name__ == "__main__":
    main()
