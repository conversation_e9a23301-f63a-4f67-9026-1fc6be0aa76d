{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nfunction ResetPasswordComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"mat-icon\", 6);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Invalid Reset Link\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \" This password reset link is invalid or has expired. Please request a new password reset. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function ResetPasswordComponent_div_10_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.requestNewReset());\n    });\n    i0.ɵɵtext(8, \" Request New Reset \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ResetPasswordComponent_div_11_mat_error_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_11_mat_error_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password must be at least 8 characters long \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_11_mat_error_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password must contain uppercase, lowercase, number and special character \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_11_mat_error_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please confirm your password \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_11_mat_error_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Passwords do not match \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResetPasswordComponent_div_11_mat_spinner_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 16);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"diameter\", 20);\n  }\n}\nfunction ResetPasswordComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"form\", 8);\n    i0.ɵɵlistener(\"ngSubmit\", function ResetPasswordComponent_div_11_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(2, \"mat-form-field\", 9)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 10);\n    i0.ɵɵelementStart(6, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ResetPasswordComponent_div_11_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.hidePassword = !ctx_r1.hidePassword);\n    });\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, ResetPasswordComponent_div_11_mat_error_9_Template, 2, 0, \"mat-error\", 3)(10, ResetPasswordComponent_div_11_mat_error_10_Template, 2, 0, \"mat-error\", 3)(11, ResetPasswordComponent_div_11_mat_error_11_Template, 2, 0, \"mat-error\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"mat-form-field\", 9)(13, \"mat-label\");\n    i0.ɵɵtext(14, \"Confirm New Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 12);\n    i0.ɵɵelementStart(16, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ResetPasswordComponent_div_11_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.hideConfirmPassword = !ctx_r1.hideConfirmPassword);\n    });\n    i0.ɵɵelementStart(17, \"mat-icon\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, ResetPasswordComponent_div_11_mat_error_19_Template, 2, 0, \"mat-error\", 3)(20, ResetPasswordComponent_div_11_mat_error_20_Template, 2, 0, \"mat-error\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 13)(22, \"h4\");\n    i0.ɵɵtext(23, \"Password Requirements:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"ul\")(25, \"li\")(26, \"mat-icon\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28, \" At least 8 characters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"li\")(30, \"mat-icon\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32, \" One uppercase letter \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"li\")(34, \"mat-icon\");\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(36, \" One lowercase letter \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"li\")(38, \"mat-icon\");\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(40, \" One number \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"li\")(42, \"mat-icon\");\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(45, \"button\", 14);\n    i0.ɵɵtemplate(46, ResetPasswordComponent_div_11_mat_spinner_46_Template, 1, 1, \"mat-spinner\", 15);\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_11_0;\n    let tmp_12_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.resetPasswordForm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_2_0 = ctx_r1.resetPasswordForm.get(\"password\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r1.resetPasswordForm.get(\"password\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵproperty(\"type\", ctx_r1.hidePassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.hidePassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx_r1.resetPasswordForm.get(\"password\")) == null ? null : tmp_5_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx_r1.resetPasswordForm.get(\"password\")) == null ? null : tmp_6_0.hasError(\"minlength\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = ctx_r1.resetPasswordForm.get(\"password\")) == null ? null : tmp_7_0.hasError(\"pattern\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_8_0 = ctx_r1.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx_r1.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_8_0.touched));\n    i0.ɵɵproperty(\"type\", ctx_r1.hideConfirmPassword ? \"password\" : \"text\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_11_0 = ctx_r1.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_11_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_12_0 = ctx_r1.resetPasswordForm.get(\"confirmPassword\")) == null ? null : tmp_12_0.hasError(\"mismatch\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"valid\", ctx_r1.hasMinLength());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.hasMinLength() ? \"check\" : \"close\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"valid\", ctx_r1.hasUppercase());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.hasUppercase() ? \"check\" : \"close\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"valid\", ctx_r1.hasLowercase());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.hasLowercase() ? \"check\" : \"close\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"valid\", ctx_r1.hasNumber());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.hasNumber() ? \"check\" : \"close\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"valid\", ctx_r1.hasSpecialChar());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.hasSpecialChar() ? \"check\" : \"close\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate3(\" One special character (!@#$%^&*(),.?\\\"\", \":\", \"\", \"{\", \"\", \"}\", \"|<>) \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.resetPasswordForm.invalid || ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isLoading ? \"Resetting...\" : \"Reset Password\", \" \");\n  }\n}\nexport let ResetPasswordComponent = /*#__PURE__*/(() => {\n  class ResetPasswordComponent {\n    constructor(fb, authService, router, route, snackBar) {\n      this.fb = fb;\n      this.authService = authService;\n      this.router = router;\n      this.route = route;\n      this.snackBar = snackBar;\n      this.isLoading = false;\n      this.hidePassword = true;\n      this.hideConfirmPassword = true;\n      this.token = null;\n      this.isTokenValid = true;\n    }\n    ngOnInit() {\n      // Get token from URL query parameters\n      this.token = this.route.snapshot.queryParamMap.get('token');\n      if (!this.token) {\n        this.isTokenValid = false;\n        this.snackBar.open('Invalid reset link. Please request a new password reset.', 'Close', {\n          duration: 5000,\n          panelClass: ['error-snackbar']\n        });\n      }\n      this.resetPasswordForm = this.fb.group({\n        password: ['', [Validators.required, Validators.minLength(8), Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/)]],\n        confirmPassword: ['', [Validators.required]]\n      }, {\n        validators: this.passwordMatchValidator\n      });\n    }\n    passwordMatchValidator(group) {\n      const password = group.get('password');\n      const confirmPassword = group.get('confirmPassword');\n      if (password && confirmPassword && password.value !== confirmPassword.value) {\n        confirmPassword.setErrors({\n          mismatch: true\n        });\n        return {\n          mismatch: true\n        };\n      }\n      return null;\n    }\n    // Password validation methods\n    hasMinLength() {\n      const password = this.resetPasswordForm.get('password')?.value || '';\n      return password.length >= 8;\n    }\n    hasUppercase() {\n      const password = this.resetPasswordForm.get('password')?.value || '';\n      return /[A-Z]/.test(password);\n    }\n    hasLowercase() {\n      const password = this.resetPasswordForm.get('password')?.value || '';\n      return /[a-z]/.test(password);\n    }\n    hasNumber() {\n      const password = this.resetPasswordForm.get('password')?.value || '';\n      return /[0-9]/.test(password);\n    }\n    hasSpecialChar() {\n      const password = this.resetPasswordForm.get('password')?.value || '';\n      return /[!@#$%^&*(),.?\":{}|<>]/.test(password);\n    }\n    passwordsMatch() {\n      const password = this.resetPasswordForm.get('password')?.value;\n      const confirmPassword = this.resetPasswordForm.get('confirmPassword')?.value;\n      return password === confirmPassword && password && confirmPassword;\n    }\n    onSubmit() {\n      if (this.resetPasswordForm.valid && !this.isLoading && this.token) {\n        this.isLoading = true;\n        const password = this.resetPasswordForm.get('password')?.value;\n        this.authService.resetPassword({\n          token: this.token,\n          password: password,\n          confirmPassword: password\n        }).subscribe({\n          next: response => {\n            this.isLoading = false;\n            this.snackBar.open('Password reset successfully! You can now login with your new password.', 'Close', {\n              duration: 5000,\n              panelClass: ['success-snackbar']\n            });\n            // Redirect to login page after successful reset\n            setTimeout(() => {\n              this.router.navigate(['/auth/login']);\n            }, 2000);\n          },\n          error: error => {\n            this.isLoading = false;\n            const errorMessage = error?.error?.message || 'Failed to reset password. Please try again.';\n            this.snackBar.open(errorMessage, 'Close', {\n              duration: 5000,\n              panelClass: ['error-snackbar']\n            });\n            // If token is invalid, redirect to forgot password\n            if (errorMessage.includes('Invalid or expired')) {\n              setTimeout(() => {\n                this.router.navigate(['/auth/forgot-password']);\n              }, 3000);\n            }\n          }\n        });\n      }\n    }\n    goBackToLogin() {\n      this.router.navigate(['/auth/login']);\n    }\n    requestNewReset() {\n      this.router.navigate(['/auth/forgot-password']);\n    }\n    static #_ = this.ɵfac = function ResetPasswordComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ResetPasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ResetPasswordComponent,\n      selectors: [[\"app-reset-password\"]],\n      standalone: false,\n      decls: 17,\n      vars: 2,\n      consts: [[1, \"auth-container\"], [1, \"auth-card\"], [\"class\", \"error-message\", 4, \"ngIf\"], [4, \"ngIf\"], [\"mat-button\", \"\", \"color\", \"accent\", 1, \"full-width\", 3, \"click\"], [1, \"error-message\"], [1, \"error-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"full-width\", 3, \"click\"], [1, \"auth-form\", 3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"Enter new password\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"Confirm new password\", 3, \"type\"], [1, \"password-requirements\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"submit-btn\", 3, \"disabled\"], [\"style\", \"display: inline-block; margin-right: 8px;\", 3, \"diameter\", 4, \"ngIf\"], [2, \"display\", \"inline-block\", \"margin-right\", \"8px\", 3, \"diameter\"]],\n      template: function ResetPasswordComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"lock_reset\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Reset Password \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n          i0.ɵɵtext(8, \" Enter your new password below \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-card-content\");\n          i0.ɵɵtemplate(10, ResetPasswordComponent_div_10_Template, 9, 0, \"div\", 2)(11, ResetPasswordComponent_div_11_Template, 48, 35, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"mat-card-actions\")(13, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function ResetPasswordComponent_Template_button_click_13_listener() {\n            return ctx.goBackToLogin();\n          });\n          i0.ɵɵelementStart(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"arrow_back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(16, \" Back to Login \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isTokenValid);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isTokenValid);\n        }\n      },\n      dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, i8.MatInput, i9.MatButton, i9.MatIconButton, i10.MatIcon, i11.MatProgressSpinner],\n      styles: [\".auth-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;padding:20px;background:linear-gradient(135deg,#667eea,#764ba2)}.auth-card[_ngcontent-%COMP%]{width:100%;max-width:450px;border-radius:12px;box-shadow:0 10px 30px #0000001a}.auth-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px;margin-top:20px}.full-width[_ngcontent-%COMP%]{width:100%}.submit-btn[_ngcontent-%COMP%]{height:48px;font-size:16px;font-weight:500;margin-top:10px}.password-requirements[_ngcontent-%COMP%]{background-color:#f5f5f5;padding:16px;border-radius:6px;margin-top:10px}.password-requirements[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 12px;font-size:14px;font-weight:500;color:#333}.password-requirements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none;padding:0;margin:0}.password-requirements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;padding:4px 0;font-size:13px;color:#666;transition:color .3s ease}.password-requirements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li.valid[_ngcontent-%COMP%]{color:#4caf50}.password-requirements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li.valid[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#4caf50}.password-requirements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;height:16px;width:16px;color:#f44336}.error-message[_ngcontent-%COMP%]{text-align:center;padding:20px 0}.error-message[_ngcontent-%COMP%]   .error-icon[_ngcontent-%COMP%]{font-size:48px;height:48px;width:48px;color:#f44336;margin-bottom:16px}.error-message[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#333;margin-bottom:16px;font-weight:500}.error-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;line-height:1.5;margin-bottom:20px}mat-card-header[_ngcontent-%COMP%]{text-align:center;padding-bottom:0}mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:8px;font-size:24px;color:#333}mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:28px;height:28px;width:28px}mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%]{color:#666;margin-top:8px;font-size:14px}mat-card-actions[_ngcontent-%COMP%]{padding-top:0}.is-invalid[_ngcontent-%COMP%]{border-color:#f44336}@media (max-width: 600px){.auth-container[_ngcontent-%COMP%]{padding:10px}.auth-card[_ngcontent-%COMP%]{max-width:100%}.password-requirements[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:13px}.password-requirements[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{font-size:12px}}\"]\n    });\n  }\n  return ResetPasswordComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}