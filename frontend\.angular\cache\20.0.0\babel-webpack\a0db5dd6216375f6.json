{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { catchError, tap } from 'rxjs/operators';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"./auth.service\";\nexport let OAuthService = /*#__PURE__*/(() => {\n  class OAuthService {\n    constructor(http, router, authService) {\n      this.http = http;\n      this.router = router;\n      this.authService = authService;\n      this.oauthProviders = [{\n        name: 'google',\n        displayName: 'Google',\n        icon: 'fab fa-google',\n        color: '#db4437'\n      }, {\n        name: 'github',\n        displayName: 'GitHub',\n        icon: 'fab fa-github',\n        color: '#333'\n      }, {\n        name: 'microsoft',\n        displayName: 'Microsoft',\n        icon: 'fab fa-microsoft',\n        color: '#00a1f1'\n      }];\n    }\n    getAvailableProviders() {\n      return this.oauthProviders;\n    }\n    getOAuthUrl(provider) {\n      return this.http.get(`${environment.apiUrl}/auth/oauth/${provider}/url`).pipe(catchError(this.handleError));\n    }\n    initiateOAuthLogin(provider) {\n      this.getOAuthUrl(provider).subscribe({\n        next: response => {\n          // Store the provider in session storage for callback handling\n          sessionStorage.setItem('oauth_provider', provider);\n          sessionStorage.setItem('oauth_redirect', this.router.url);\n          // Redirect to OAuth provider\n          window.location.href = response.url;\n        },\n        error: error => {\n          console.error(`Failed to get ${provider} OAuth URL:`, error);\n          // Handle error - show notification to user\n        }\n      });\n    }\n    handleOAuthCallback(code, state) {\n      const provider = sessionStorage.getItem('oauth_provider');\n      const redirectUrl = sessionStorage.getItem('oauth_redirect') || '/dashboard';\n      if (!provider) {\n        return throwError(() => new Error('OAuth provider not found in session'));\n      }\n      const callbackData = {\n        code\n      };\n      if (state) {\n        callbackData.state = state;\n      }\n      return this.http.post(`${environment.apiUrl}/auth/oauth/${provider}/callback`, callbackData).pipe(tap(response => {\n        if (response.token) {\n          // Clear OAuth session data\n          sessionStorage.removeItem('oauth_provider');\n          sessionStorage.removeItem('oauth_redirect');\n          // Set authentication token\n          this.authService.setToken(response.token);\n          // Navigate to intended destination\n          this.router.navigate([redirectUrl]);\n        }\n      }), catchError(this.handleError));\n    }\n    /**\n     * Exchange authorization code for JWT token (secure OAuth flow)\n     */\n    exchangeAuthorizationCode(code) {\n      console.log('🔄 OAuth Service - Exchanging authorization code for token');\n      return this.http.post(`${environment.apiUrl}/auth/oauth/exchange-token`, {\n        code\n      }).pipe(tap(response => {\n        console.log('✅ OAuth Service - Token exchange successful');\n      }), catchError(error => {\n        console.error('❌ OAuth Service - Token exchange failed:', error);\n        return throwError(error);\n      }));\n    }\n    isOAuthUser(user) {\n      return !!(user?.oauthProvider && (user?.googleId || user?.githubId || user?.microsoftId));\n    }\n    getOAuthProviderName(user) {\n      if (user?.googleId) return 'Google';\n      if (user?.githubId) return 'GitHub';\n      if (user?.microsoftId) return 'Microsoft';\n      return 'Unknown';\n    }\n    getOAuthProviderIcon(user) {\n      if (user?.googleId) return 'fab fa-google';\n      if (user?.githubId) return 'fab fa-github';\n      if (user?.microsoftId) return 'fab fa-microsoft';\n      return 'fas fa-user';\n    }\n    getOAuthProviderColor(user) {\n      if (user?.googleId) return '#db4437';\n      if (user?.githubId) return '#333';\n      if (user?.microsoftId) return '#00a1f1';\n      return '#6c757d';\n    }\n    // Link OAuth account to existing user (if needed in future)\n    linkOAuthAccount(provider) {\n      this.getOAuthUrl(provider).subscribe({\n        next: response => {\n          sessionStorage.setItem('oauth_action', 'link');\n          sessionStorage.setItem('oauth_provider', provider);\n          window.location.href = response.url;\n        },\n        error: error => {\n          console.error(`Failed to link ${provider} account:`, error);\n        }\n      });\n    }\n    // Unlink OAuth account (if needed in future)\n    unlinkOAuthAccount(provider) {\n      const headers = this.authService.getAuthHeaders();\n      return this.http.delete(`${environment.apiUrl}/auth/oauth/${provider}/unlink`, {\n        headers\n      }).pipe(catchError(this.handleError));\n    }\n    handleError(error) {\n      let errorMessage = 'OAuth authentication error occurred';\n      if (error.error instanceof ErrorEvent) {\n        errorMessage = error.error.message;\n      } else {\n        errorMessage = error.error?.message || error.message || `Error Code: ${error.status}`;\n      }\n      console.error('OAuth Service Error:', error);\n      return throwError(() => new Error(errorMessage));\n    }\n    static #_ = this.ɵfac = function OAuthService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OAuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.AuthService));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: OAuthService,\n      factory: OAuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return OAuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}