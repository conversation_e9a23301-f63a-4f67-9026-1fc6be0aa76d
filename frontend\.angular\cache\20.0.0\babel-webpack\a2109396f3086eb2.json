{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Downloads/study/apps/ai/cody/Modular backend secure user system and payment_Cody/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators, ReactiveFormsModule } from '@angular/forms';\nimport { CommonModule } from '@angular/common';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { ConfirmationDialogComponent } from '../confirmation-dialog/confirmation-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/account-deletion.service\";\nimport * as i3 from \"../../services/auth.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/material/dialog\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/button\";\nimport * as i11 from \"@angular/material/checkbox\";\nimport * as i12 from \"@angular/material/form-field\";\nimport * as i13 from \"@angular/material/input\";\nimport * as i14 from \"@angular/material/progress-spinner\";\nfunction AccountDeletionComponent_div_8_mat_error_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please enter a valid number between 1 and 365 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountDeletionComponent_div_8_div_47_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(item_r3);\n  }\n}\nfunction AccountDeletionComponent_div_8_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"h4\");\n    i0.ɵɵtext(2, \"Data Preservation Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\");\n    i0.ɵɵtemplate(4, AccountDeletionComponent_div_8_div_47_li_4_Template, 2, 1, \"li\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 26)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Preserved data will be kept for \");\n    i0.ɵɵelementStart(9, \"strong\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.preservedDataSummary);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.retentionPeriodText);\n  }\n}\nfunction AccountDeletionComponent_div_8_mat_error_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" You must confirm before proceeding \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountDeletionComponent_div_8_mat_spinner_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 27);\n  }\n}\nfunction AccountDeletionComponent_div_8_mat_icon_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"delete_forever\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountDeletionComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"mat-icon\", 2);\n    i0.ɵɵtext(3, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\")(5, \"strong\");\n    i0.ɵɵtext(6, \"Warning:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" Account deletion is permanent and cannot be undone immediately. Please carefully review your preferences below. \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"form\", 6);\n    i0.ɵɵlistener(\"ngSubmit\", function AccountDeletionComponent_div_8_Template_form_ngSubmit_8_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.requestDeletion());\n    });\n    i0.ɵɵelementStart(9, \"h3\");\n    i0.ɵɵtext(10, \"Data Preservation Preferences\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 7);\n    i0.ɵɵtext(12, \" Choose which data you want to preserve for potential restoration: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 8)(14, \"mat-checkbox\", 9)(15, \"strong\");\n    i0.ɵɵtext(16, \"Preserve Payment Data\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 10);\n    i0.ɵɵtext(18, \"Keep payment methods and billing information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"mat-checkbox\", 11)(20, \"strong\");\n    i0.ɵɵtext(21, \"Preserve Transaction History\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 10);\n    i0.ɵɵtext(23, \"Keep records of past transactions and orders\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"mat-checkbox\", 12)(25, \"strong\");\n    i0.ɵɵtext(26, \"Preserve Profile Backup\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 10);\n    i0.ɵɵtext(28, \"Keep a backup of your profile information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"mat-checkbox\", 13)(30, \"strong\");\n    i0.ɵɵtext(31, \"Preserve Security Logs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 10);\n    i0.ɵɵtext(33, \"Keep login history and security events\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"mat-form-field\", 14)(35, \"mat-label\");\n    i0.ɵɵtext(36, \"Data Retention Period (days)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(37, \"input\", 15);\n    i0.ɵɵelementStart(38, \"mat-hint\");\n    i0.ɵɵtext(39, \"How long to keep your preserved data (1-365 days)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(40, AccountDeletionComponent_div_8_mat_error_40_Template, 2, 0, \"mat-error\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"mat-form-field\", 14)(42, \"mat-label\");\n    i0.ɵɵtext(43, \"Reason for Deletion (Optional)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(44, \"textarea\", 17);\n    i0.ɵɵelementStart(45, \"mat-hint\");\n    i0.ɵɵtext(46, \"This helps us improve our service\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(47, AccountDeletionComponent_div_8_div_47_Template, 11, 2, \"div\", 18);\n    i0.ɵɵelementStart(48, \"mat-checkbox\", 19)(49, \"strong\");\n    i0.ɵɵtext(50, \"I understand that this action cannot be undone immediately\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(51, AccountDeletionComponent_div_8_mat_error_51_Template, 2, 0, \"mat-error\", 16);\n    i0.ɵɵelementStart(52, \"div\", 20)(53, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function AccountDeletionComponent_div_8_Template_button_click_53_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToSettings());\n    });\n    i0.ɵɵelementStart(54, \"mat-icon\");\n    i0.ɵɵtext(55, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(56, \" Back to Settings \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"button\", 22);\n    i0.ɵɵtemplate(58, AccountDeletionComponent_div_8_mat_spinner_58_Template, 1, 0, \"mat-spinner\", 23)(59, AccountDeletionComponent_div_8_mat_icon_59_Template, 2, 0, \"mat-icon\", 16);\n    i0.ɵɵtext(60, \" Request Account Deletion \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.deletionForm);\n    i0.ɵɵadvance(32);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r1.deletionForm.get(\"customRetentionPeriod\")) == null ? null : tmp_2_0.invalid);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.preservedDataSummary.length > 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r1.deletionForm.get(\"confirmDeletion\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r1.deletionForm.get(\"confirmDeletion\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading || ctx_r1.deletionForm.invalid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n  }\n}\nfunction AccountDeletionComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 28)(2, \"mat-icon\", 29);\n    i0.ɵɵtext(3, \"mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Check Your Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \" We've sent a confirmation email with a link to complete your account deletion. Please check your inbox and follow the instructions. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 30)(9, \"strong\");\n    i0.ɵɵtext(10, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11, \" The confirmation link will expire in 24 hours. \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 20)(13, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function AccountDeletionComponent_div_9_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.step = \"preferences\");\n    });\n    i0.ɵɵelementStart(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16, \" Change Preferences \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function AccountDeletionComponent_div_9_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.checkExistingDeletionStatus());\n    });\n    i0.ɵɵelementStart(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20, \" Refresh Status \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction AccountDeletionComponent_div_10_div_8_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"strong\");\n    i0.ɵɵtext(2, \"Data Expiry:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(4, 1, ctx_r1.currentStatus.deletionRecord.dataExpiryDate, \"medium\"), \" \");\n  }\n}\nfunction AccountDeletionComponent_div_10_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"h4\");\n    i0.ɵɵtext(2, \"Request Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 39)(4, \"strong\");\n    i0.ɵɵtext(5, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 39)(8, \"strong\");\n    i0.ɵɵtext(9, \"Requested:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 39)(13, \"strong\");\n    i0.ɵɵtext(14, \"Status:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 40);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"titlecase\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(18, AccountDeletionComponent_div_10_div_8_div_18_Template, 5, 4, \"div\", 41);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.currentStatus.deletionRecord.email, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(11, 4, ctx_r1.currentStatus.deletionRecord.deletionRequestedAt, \"medium\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(17, 7, ctx_r1.currentStatus.deletionRecord.deletionStatus));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStatus.deletionRecord.dataExpiryDate);\n  }\n}\nfunction AccountDeletionComponent_div_10_mat_spinner_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 27);\n  }\n}\nfunction AccountDeletionComponent_div_10_mat_icon_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"cancel\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AccountDeletionComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 33)(2, \"mat-icon\", 34);\n    i0.ɵɵtext(3, \"pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5, \"Deletion Request Pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"Your account deletion request is currently pending confirmation.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AccountDeletionComponent_div_10_div_8_Template, 19, 9, \"div\", 35);\n    i0.ɵɵelementStart(9, \"div\", 20)(10, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function AccountDeletionComponent_div_10_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.checkExistingDeletionStatus());\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"refresh\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Refresh Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function AccountDeletionComponent_div_10_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.cancelDeletion());\n    });\n    i0.ɵɵtemplate(15, AccountDeletionComponent_div_10_mat_spinner_15_Template, 1, 0, \"mat-spinner\", 23)(16, AccountDeletionComponent_div_10_mat_icon_16_Template, 2, 0, \"mat-icon\", 16);\n    i0.ɵɵtext(17, \" Cancel Deletion Request \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStatus.deletionRecord);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n  }\n}\nexport let AccountDeletionComponent = /*#__PURE__*/(() => {\n  class AccountDeletionComponent {\n    constructor(fb, accountDeletionService, authService, router, snackBar, dialog) {\n      this.fb = fb;\n      this.accountDeletionService = accountDeletionService;\n      this.authService = authService;\n      this.router = router;\n      this.snackBar = snackBar;\n      this.dialog = dialog;\n      this.currentStatus = null;\n      this.isLoading = false;\n      this.step = 'preferences';\n      this.deletionForm = this.fb.group({\n        preservePaymentData: [true],\n        preserveTransactionHistory: [true],\n        preserveProfileData: [false],\n        preserveSecurityLogs: [false],\n        customRetentionPeriod: [30, [Validators.min(1), Validators.max(365)]],\n        reason: ['', Validators.maxLength(500)],\n        confirmDeletion: [false, Validators.requiredTrue]\n      });\n    }\n    ngOnInit() {\n      this.checkExistingDeletionStatus();\n    }\n    checkExistingDeletionStatus() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const status = yield _this.accountDeletionService.getDeletionStatus().toPromise();\n          if (status && status.hasPendingDeletion && status.deletionRecord) {\n            _this.currentStatus = status;\n            _this.step = 'status';\n          }\n        } catch (error) {\n          console.error('Error checking deletion status:', error);\n        }\n      })();\n    }\n    requestDeletion() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        if (_this2.deletionForm.invalid) {\n          _this2.markFormGroupTouched();\n          return;\n        }\n        const dialogRef = _this2.dialog.open(ConfirmationDialogComponent, {\n          width: '400px',\n          data: {\n            title: 'Confirm Account Deletion',\n            message: 'Are you absolutely sure you want to delete your account? This action cannot be undone immediately.',\n            confirmText: 'Yes, Delete My Account',\n            cancelText: 'Cancel',\n            isDangerous: true\n          }\n        });\n        const confirmed = yield dialogRef.afterClosed().toPromise();\n        if (!confirmed) return;\n        _this2.isLoading = true;\n        try {\n          const preferences = {\n            preservePaymentData: _this2.deletionForm.value.preservePaymentData,\n            preserveTransactionHistory: _this2.deletionForm.value.preserveTransactionHistory,\n            preserveProfileData: _this2.deletionForm.value.preserveProfileData,\n            preserveSecurityLogs: _this2.deletionForm.value.preserveSecurityLogs,\n            customRetentionPeriod: _this2.deletionForm.value.customRetentionPeriod,\n            reason: _this2.deletionForm.value.reason?.trim() || undefined\n          };\n          const result = yield _this2.accountDeletionService.requestAccountDeletion(preferences).toPromise();\n          _this2.snackBar.open('Account deletion requested! Please check your email for confirmation instructions.', 'Close', {\n            duration: 8000,\n            panelClass: ['snack-bar-warning']\n          });\n          _this2.step = 'confirmation';\n          yield _this2.checkExistingDeletionStatus();\n        } catch (error) {\n          console.error('Error requesting deletion:', error);\n          _this2.snackBar.open(error.message || 'Failed to request account deletion. Please try again.', 'Close', {\n            duration: 5000,\n            panelClass: ['snack-bar-error']\n          });\n        } finally {\n          _this2.isLoading = false;\n        }\n      })();\n    }\n    cancelDeletion() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this3.currentStatus?.deletionRecord?.id) return;\n        const dialogRef = _this3.dialog.open(ConfirmationDialogComponent, {\n          width: '400px',\n          data: {\n            title: 'Cancel Account Deletion',\n            message: 'Are you sure you want to cancel the account deletion request?',\n            confirmText: 'Yes, Cancel Deletion',\n            cancelText: 'Keep Deletion Request'\n          }\n        });\n        const confirmed = yield dialogRef.afterClosed().toPromise();\n        if (!confirmed) return;\n        _this3.isLoading = true;\n        try {\n          yield _this3.accountDeletionService.cancelDeletion().toPromise();\n          _this3.snackBar.open('Account deletion request has been cancelled.', 'Close', {\n            duration: 5000,\n            panelClass: ['snack-bar-success']\n          });\n          _this3.currentStatus = null;\n          _this3.step = 'preferences';\n          _this3.deletionForm.reset();\n        } catch (error) {\n          console.error('Error cancelling deletion:', error);\n          _this3.snackBar.open(error.message || 'Failed to cancel account deletion. Please try again.', 'Close', {\n            duration: 5000,\n            panelClass: ['snack-bar-error']\n          });\n        } finally {\n          _this3.isLoading = false;\n        }\n      })();\n    }\n    goToSettings() {\n      this.router.navigate(['/dashboard/settings']);\n    }\n    markFormGroupTouched() {\n      Object.keys(this.deletionForm.controls).forEach(key => {\n        this.deletionForm.get(key)?.markAsTouched();\n      });\n    }\n    get preservedDataSummary() {\n      const preferences = this.deletionForm.value;\n      const preserved = [];\n      if (preferences.preservePaymentData) preserved.push('Payment data');\n      if (preferences.preserveTransactionHistory) preserved.push('Transaction history');\n      if (preferences.preserveProfileData) preserved.push('Profile data');\n      if (preferences.preserveSecurityLogs) preserved.push('Security logs');\n      return preserved;\n    }\n    get retentionPeriodText() {\n      const days = this.deletionForm.value.customRetentionPeriod || 30;\n      if (days === 1) return '1 day';\n      if (days < 30) return `${days} days`;\n      if (days === 30) return '1 month';\n      if (days < 365) return `${Math.round(days / 30)} months`;\n      return '1 year';\n    }\n    static #_ = this.ɵfac = function AccountDeletionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AccountDeletionComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AccountDeletionService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.Router), i0.ɵɵdirectiveInject(i5.MatSnackBar), i0.ɵɵdirectiveInject(i6.MatDialog));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AccountDeletionComponent,\n      selectors: [[\"app-account-deletion\"]],\n      decls: 11,\n      vars: 3,\n      consts: [[1, \"account-deletion-container\"], [1, \"deletion-card\"], [\"color\", \"warn\"], [\"class\", \"step-content\", 4, \"ngIf\"], [1, \"step-content\"], [1, \"warning-message\"], [3, \"ngSubmit\", \"formGroup\"], [1, \"section-description\"], [1, \"preferences-section\"], [\"formControlName\", \"preservePaymentData\"], [1, \"preference-description\"], [\"formControlName\", \"preserveTransactionHistory\"], [\"formControlName\", \"preserveProfileData\"], [\"formControlName\", \"preserveSecurityLogs\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"customRetentionPeriod\", \"min\", \"1\", \"max\", \"365\", \"placeholder\", \"30\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"reason\", \"rows\", \"3\", \"placeholder\", \"Tell us why you're deleting your account...\"], [\"class\", \"summary-section\", 4, \"ngIf\"], [\"formControlName\", \"confirmDeletion\", 1, \"confirmation-checkbox\"], [1, \"actions\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"summary-section\"], [4, \"ngFor\", \"ngForOf\"], [1, \"retention-info\"], [\"diameter\", \"20\"], [1, \"confirmation-message\"], [\"color\", \"primary\", 1, \"large-icon\"], [1, \"email-note\"], [\"mat-button\", \"\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"status-message\"], [\"color\", \"warn\", 1, \"large-icon\"], [\"class\", \"status-details\", 4, \"ngIf\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 3, \"click\", \"disabled\"], [1, \"status-details\"], [1, \"detail-item\"], [1, \"status-badge\"], [\"class\", \"detail-item\", 4, \"ngIf\"]],\n      template: function AccountDeletionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\", 2);\n          i0.ɵɵtext(5, \"warning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Account Deletion \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"mat-card-content\");\n          i0.ɵɵtemplate(8, AccountDeletionComponent_div_8_Template, 61, 7, \"div\", 3)(9, AccountDeletionComponent_div_9_Template, 21, 0, \"div\", 3)(10, AccountDeletionComponent_div_10_Template, 18, 4, \"div\", 3);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.step === \"preferences\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.step === \"confirmation\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.step === \"status\" && ctx.currentStatus);\n        }\n      },\n      dependencies: [CommonModule, i7.NgForOf, i7.NgIf, i7.TitleCasePipe, i7.DatePipe, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName, MatCardModule, i8.MatCard, i8.MatCardContent, i8.MatCardHeader, i8.MatCardTitle, MatIconModule, i9.MatIcon, MatButtonModule, i10.MatButton, MatCheckboxModule, i11.MatCheckbox, MatFormFieldModule, i12.MatFormField, i12.MatLabel, i12.MatHint, i12.MatError, MatInputModule, i13.MatInput, MatProgressSpinnerModule, i14.MatProgressSpinner],\n      styles: [\".account-deletion-container[_ngcontent-%COMP%]{max-width:800px;margin:0 auto;padding:20px}.deletion-card[_ngcontent-%COMP%]{margin-bottom:20px}.mat-card-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px}.warning-message[_ngcontent-%COMP%], .confirmation-message[_ngcontent-%COMP%], .status-message[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:15px;padding:15px;border-radius:8px;margin-bottom:20px}.warning-message[_ngcontent-%COMP%]{background-color:#fff3cd;border:1px solid #ffeaa7;color:#856404}.confirmation-message[_ngcontent-%COMP%], .status-message[_ngcontent-%COMP%]{background-color:#e3f2fd;border:1px solid #bbdefb;color:#1565c0;flex-direction:column;text-align:center}.large-icon[_ngcontent-%COMP%]{font-size:48px;width:48px;height:48px}.step-content[_ngcontent-%COMP%]{margin:20px 0}.section-description[_ngcontent-%COMP%]{color:#666;margin-bottom:15px}.preferences-section[_ngcontent-%COMP%]{margin:20px 0;display:flex;flex-direction:column;gap:15px}.preference-description[_ngcontent-%COMP%]{font-size:12px;color:#888;margin-top:4px}.full-width[_ngcontent-%COMP%]{width:100%;margin:10px 0}.summary-section[_ngcontent-%COMP%]{background-color:#f8f9fa;padding:15px;border-radius:8px;border:1px solid #e9ecef;margin:20px 0}.summary-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 10px;color:#495057}.summary-section[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:10px 0;padding-left:20px}.retention-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:5px;margin-top:10px;font-size:14px;color:#6c757d}.confirmation-checkbox[_ngcontent-%COMP%]{margin:20px 0}.status-details[_ngcontent-%COMP%]{background-color:#f8f9fa;padding:15px;border-radius:8px;margin:20px 0}.status-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 15px;color:#495057}.detail-item[_ngcontent-%COMP%]{margin:8px 0;display:flex;gap:10px}.detail-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{min-width:120px}.status-badge[_ngcontent-%COMP%]{background-color:#ffc107;color:#212529;padding:2px 8px;border-radius:12px;font-size:12px;font-weight:500}.actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-top:20px;gap:15px}.actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.email-note[_ngcontent-%COMP%]{font-size:14px;color:#6c757d;margin-top:10px}@media (max-width: 768px){.account-deletion-container[_ngcontent-%COMP%]{padding:10px}.actions[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;justify-content:center}.warning-message[_ngcontent-%COMP%], .confirmation-message[_ngcontent-%COMP%], .status-message[_ngcontent-%COMP%]{flex-direction:column;text-align:center}}\"]\n    });\n  }\n  return AccountDeletionComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}