{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RateLimitService } from '../../services/rate-limit.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/rate-limit.service\";\nimport * as i2 from \"@angular/common\";\nfunction RateLimitNotificationComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4);\n    i0.ɵɵelement(4, \"i\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 6)(6, \"h4\", 7);\n    i0.ɵɵtext(7, \"Rate Limit Exceeded\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 8);\n    i0.ɵɵtext(9, \"Too many requests - please wait\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"button\", 9);\n    i0.ɵɵpipe(11, \"async\");\n    i0.ɵɵlistener(\"click\", function RateLimitNotificationComponent_div_0_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.dismissNotification());\n    });\n    i0.ɵɵelement(12, \"i\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 12)(15, \"div\", 13)(16, \"span\", 14);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 15);\n    i0.ɵɵtext(19, \"MIN\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 16);\n    i0.ɵɵtext(21, \":\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 13)(23, \"span\", 14);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\", 15);\n    i0.ɵɵtext(26, \"SEC\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"div\", 17);\n    i0.ɵɵelement(28, \"div\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"p\", 19);\n    i0.ɵɵtext(30, \" Please wait \");\n    i0.ɵɵelementStart(31, \"strong\");\n    i0.ɵɵtext(32);\n    i0.ɵɵpipe(33, \"async\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(34, \" before making another request \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 20)(36, \"div\", 21);\n    i0.ɵɵelement(37, \"i\", 22);\n    i0.ɵɵelementStart(38, \"div\", 23)(39, \"p\")(40, \"strong\");\n    i0.ɵɵtext(41, \"Why am I seeing this?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"p\");\n    i0.ɵɵtext(43, \"Rate limiting protects our service from overload and ensures fair usage for all users.\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(44, \"div\", 24)(45, \"button\", 25);\n    i0.ɵɵpipe(46, \"async\");\n    i0.ɵɵlistener(\"click\", function RateLimitNotificationComponent_div_0_Template_button_click_45_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.refreshPage());\n    });\n    i0.ɵɵelement(47, \"i\", 26);\n    i0.ɵɵtext(48, \" Try Again \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"disabled\", ((tmp_1_0 = i0.ɵɵpipeBind1(11, 7, ctx_r1.rateLimit$)) == null ? null : tmp_1_0.remainingTime) > 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.getMinutes());\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.getSeconds());\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getProgressPercentage(), \"%\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatTime(((tmp_5_0 = i0.ɵɵpipeBind1(33, 9, ctx_r1.rateLimit$)) == null ? null : tmp_5_0.remainingTime) || 0));\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"disabled\", ((tmp_6_0 = i0.ɵɵpipeBind1(46, 11, ctx_r1.rateLimit$)) == null ? null : tmp_6_0.remainingTime) > 0);\n  }\n}\nexport let RateLimitNotificationComponent = /*#__PURE__*/(() => {\n  class RateLimitNotificationComponent {\n    constructor(rateLimitService) {\n      this.rateLimitService = rateLimitService;\n      this.rateLimit$ = this.rateLimitService.rateLimit$;\n    }\n    ngOnInit() {\n      // Component initialization\n    }\n    ngOnDestroy() {\n      // Cleanup if needed\n    }\n    /**\n     * Format time for display\n     */\n    formatTime(seconds) {\n      return RateLimitService.formatTimeRemaining(seconds);\n    }\n    /**\n     * Get minutes from remaining time\n     */\n    getMinutes() {\n      const status = this.rateLimitService.getCurrentStatus();\n      const minutes = Math.floor(status.remainingTime / 60);\n      return minutes.toString().padStart(2, '0');\n    }\n    /**\n     * Get seconds from remaining time\n     */\n    getSeconds() {\n      const status = this.rateLimitService.getCurrentStatus();\n      const seconds = status.remainingTime % 60;\n      return seconds.toString().padStart(2, '0');\n    }\n    /**\n     * Get progress percentage for progress bar\n     */\n    getProgressPercentage() {\n      const status = this.rateLimitService.getCurrentStatus();\n      if (status.retryAfter === 0) return 0;\n      const elapsed = status.retryAfter - status.remainingTime;\n      return elapsed / status.retryAfter * 100;\n    }\n    /**\n     * Dismiss notification (only when countdown is finished)\n     */\n    dismissNotification() {\n      const status = this.rateLimitService.getCurrentStatus();\n      if (status.remainingTime <= 0) {\n        this.rateLimitService.clearRateLimit();\n      }\n    }\n    /**\n     * Refresh the page when rate limit is cleared\n     */\n    refreshPage() {\n      const status = this.rateLimitService.getCurrentStatus();\n      if (status.remainingTime <= 0) {\n        this.rateLimitService.clearRateLimit();\n        window.location.reload();\n      }\n    }\n    static #_ = this.ɵfac = function RateLimitNotificationComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || RateLimitNotificationComponent)(i0.ɵɵdirectiveInject(i1.RateLimitService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RateLimitNotificationComponent,\n      selectors: [[\"app-rate-limit-notification\"]],\n      decls: 2,\n      vars: 3,\n      consts: [[\"class\", \"rate-limit-popup-overlay\", 4, \"ngIf\"], [1, \"rate-limit-popup-overlay\"], [1, \"rate-limit-popup\"], [1, \"popup-header\"], [1, \"header-icon\"], [1, \"fas\", \"fa-clock\"], [1, \"header-content\"], [1, \"popup-title\"], [1, \"popup-subtitle\"], [\"type\", \"button\", 1, \"close-btn\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-times\"], [1, \"countdown-section\"], [1, \"countdown-display\"], [1, \"time-box\"], [1, \"time-number\"], [1, \"time-label\"], [1, \"time-separator\"], [1, \"progress-container\"], [1, \"progress-bar\"], [1, \"progress-text\"], [1, \"message-section\"], [1, \"message-content\"], [1, \"fas\", \"fa-shield-alt\", \"message-icon\"], [1, \"message-text\"], [1, \"action-buttons\"], [1, \"btn-refresh\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-refresh\", \"me-2\"]],\n      template: function RateLimitNotificationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, RateLimitNotificationComponent_div_0_Template, 49, 13, \"div\", 0);\n          i0.ɵɵpipe(1, \"async\");\n        }\n        if (rf & 2) {\n          let tmp_0_0;\n          i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = i0.ɵɵpipeBind1(1, 1, ctx.rateLimit$)) == null ? null : tmp_0_0.isRateLimited);\n        }\n      },\n      dependencies: [CommonModule, i2.NgIf, i2.AsyncPipe],\n      styles: [\".rate-limit-popup-overlay[_ngcontent-%COMP%]{position:fixed;inset:0;background:#0009;-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px);z-index:10000;display:flex;align-items:center;justify-content:center;padding:20px;animation:_ngcontent-%COMP%_fadeIn .3s ease-out}.rate-limit-popup[_ngcontent-%COMP%]{background:linear-gradient(135deg,#fff,#f8f9fa);border-radius:16px;box-shadow:0 20px 60px #0000004d,0 0 0 1px #ffffff1a;max-width:480px;width:100%;overflow:hidden;animation:_ngcontent-%COMP%_popIn .4s cubic-bezier(.68,-.55,.265,1.55);border:2px solid #ffc107}.popup-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ff6b6b,#ee5a52);color:#fff;padding:24px;display:flex;align-items:center;position:relative}.header-icon[_ngcontent-%COMP%]{background:#fff3;border-radius:50%;width:56px;height:56px;display:flex;align-items:center;justify-content:center;margin-right:16px;font-size:24px;animation:_ngcontent-%COMP%_pulse 2s infinite}.header-content[_ngcontent-%COMP%]{flex:1}.popup-title[_ngcontent-%COMP%]{font-size:20px;font-weight:700;margin:0 0 4px;letter-spacing:-.5px}.popup-subtitle[_ngcontent-%COMP%]{font-size:14px;margin:0;opacity:.9;font-weight:400}.close-btn[_ngcontent-%COMP%]{background:#fff3;border:none;border-radius:50%;width:36px;height:36px;display:flex;align-items:center;justify-content:center;color:#fff;cursor:pointer;transition:all .2s ease;font-size:14px}.close-btn[_ngcontent-%COMP%]:hover:not(:disabled){background:#ffffff4d;transform:scale(1.1)}.close-btn[_ngcontent-%COMP%]:disabled{opacity:.4;cursor:not-allowed}.countdown-section[_ngcontent-%COMP%]{padding:32px 24px;text-align:center;background:#fff}.countdown-display[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;margin-bottom:24px;gap:8px}.time-box[_ngcontent-%COMP%]{background:linear-gradient(135deg,#667eea,#764ba2);color:#fff;padding:16px 12px;border-radius:12px;min-width:80px;box-shadow:0 4px 20px #667eea4d;text-align:center}.time-number[_ngcontent-%COMP%]{display:block;font-size:28px;font-weight:700;line-height:1;margin-bottom:4px;font-family:Segoe UI,system-ui,sans-serif}.time-label[_ngcontent-%COMP%]{display:block;font-size:10px;font-weight:600;letter-spacing:1px;opacity:.9}.time-separator[_ngcontent-%COMP%]{font-size:32px;font-weight:300;color:#667eea;animation:_ngcontent-%COMP%_blink 1s infinite}.progress-container[_ngcontent-%COMP%]{background:#e9ecef;height:8px;border-radius:4px;overflow:hidden;margin:16px 0;position:relative}.progress-bar[_ngcontent-%COMP%]{height:100%;background:linear-gradient(90deg,#ff6b6b,orange);border-radius:4px;transition:width 1s ease;position:relative;overflow:hidden}.progress-bar[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.4),transparent);animation:_ngcontent-%COMP%_shimmer 2s infinite}.progress-text[_ngcontent-%COMP%]{color:#6c757d;font-size:14px;margin:8px 0 0}.message-section[_ngcontent-%COMP%]{background:#f8f9fa;padding:20px 24px;border-top:1px solid #dee2e6}.message-content[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:12px}.message-icon[_ngcontent-%COMP%]{color:#17a2b8;font-size:18px;margin-top:2px}.message-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 8px;font-size:13px;line-height:1.4;color:#495057}.message-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child{margin-bottom:0}.action-buttons[_ngcontent-%COMP%]{padding:20px 24px;background:#fff;text-align:center}.btn-refresh[_ngcontent-%COMP%]{background:linear-gradient(135deg,#28a745,#20c997);color:#fff;border:none;padding:12px 24px;border-radius:8px;font-weight:600;font-size:14px;cursor:pointer;transition:all .3s ease;box-shadow:0 4px 15px #28a7454d}.btn-refresh[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-2px);box-shadow:0 6px 20px #28a74566}.btn-refresh[_ngcontent-%COMP%]:disabled{background:#6c757d;cursor:not-allowed;transform:none;box-shadow:none}@keyframes _ngcontent-%COMP%_fadeIn{0%{opacity:0}to{opacity:1}}@keyframes _ngcontent-%COMP%_popIn{0%{opacity:0;transform:scale(.8) translateY(-20px)}to{opacity:1;transform:scale(1) translateY(0)}}@keyframes _ngcontent-%COMP%_pulse{0%,to{transform:scale(1)}50%{transform:scale(1.05)}}@keyframes _ngcontent-%COMP%_blink{0%,50%{opacity:1}51%,to{opacity:.3}}@keyframes _ngcontent-%COMP%_shimmer{0%{left:-100%}to{left:100%}}@media (max-width: 576px){.rate-limit-popup[_ngcontent-%COMP%]{margin:10px;max-width:none}.popup-header[_ngcontent-%COMP%]{padding:20px}.countdown-section[_ngcontent-%COMP%]{padding:24px 20px}.time-box[_ngcontent-%COMP%]{min-width:70px;padding:12px 8px}.time-number[_ngcontent-%COMP%]{font-size:24px}.message-section[_ngcontent-%COMP%], .action-buttons[_ngcontent-%COMP%]{padding:16px 20px}}\"],\n      data: {\n        animation: [\n          // Add Angular animations if needed\n        ]\n      }\n    });\n  }\n  return RateLimitNotificationComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}