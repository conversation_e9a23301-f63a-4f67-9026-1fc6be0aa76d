{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { ReactiveFormsModule } from '@angular/forms';\n// Angular Material\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDividerModule } from '@angular/material/divider';\n// App Components\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { RateLimitNotificationComponent } from './components/rate-limit-notification/rate-limit-notification.component';\n// Interceptors\nimport { AuthInterceptor } from './interceptors/auth.interceptor';\n// Services\nimport { AuthService } from './services/auth.service';\nimport { PaymentService } from './services/payment.service';\nimport { TwoFactorService } from './services/two-factor.service';\nimport { LoadingService } from './services/loading.service';\nimport { RateLimitService } from './services/rate-limit.service';\nimport * as i0 from \"@angular/core\";\nexport let AppModule = /*#__PURE__*/(() => {\n  class AppModule {\n    static #_ = this.ɵfac = function AppModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [{\n        provide: HTTP_INTERCEPTORS,\n        useClass: AuthInterceptor,\n        multi: true\n      }, AuthService, PaymentService, TwoFactorService, LoadingService, RateLimitService],\n      imports: [BrowserModule, BrowserAnimationsModule, HttpClientModule, ReactiveFormsModule, AppRoutingModule, RateLimitNotificationComponent,\n      // Angular Material\n      MatToolbarModule, MatButtonModule, MatIconModule, MatMenuModule, MatSnackBarModule, MatProgressSpinnerModule, MatDividerModule]\n    });\n  }\n  return AppModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}