#!/usr/bin/env python3
"""
Test Email Verification Fix
Test the email verification endpoint to ensure it works properly with frontend
"""

import requests
import json
import time

API_BASE = "http://localhost:3002/api"
FRONTEND_ORIGIN = "http://localhost:4200"

def test_cors_preflight():
    """Test CORS preflight request"""
    print("🧪 Testing CORS preflight...")
    
    headers = {
        "Origin": FRONTEND_ORIGIN,
        "Access-Control-Request-Method": "POST",
        "Access-Control-Request-Headers": "Content-Type,Authorization"
    }
    
    try:
        response = requests.options(f"{API_BASE}/auth/verify-email", headers=headers, timeout=5)
        print(f"✅ CORS preflight status: {response.status_code}")
        print(f"✅ Access-Control-Allow-Origin: {response.headers.get('Access-Control-Allow-Origin')}")
        print(f"✅ Access-Control-Allow-Methods: {response.headers.get('Access-Control-Allow-Methods')}")
        print(f"✅ Access-Control-Allow-Headers: {response.headers.get('Access-Control-Allow-Headers')}")
        return True
    except Exception as e:
        print(f"❌ CORS preflight failed: {e}")
        return False

def test_verify_email_invalid_token():
    """Test verify-email with invalid token"""
    print("\n🧪 Testing verify-email with invalid token...")
    
    headers = {
        "Content-Type": "application/json",
        "Origin": FRONTEND_ORIGIN
    }
    
    data = {"token": "invalid-token"}
    
    try:
        response = requests.post(f"{API_BASE}/auth/verify-email", json=data, headers=headers, timeout=5)
        print(f"✅ Invalid token status: {response.status_code}")
        print(f"✅ Response: {response.json()}")
        return True
    except Exception as e:
        print(f"❌ Invalid token test failed: {e}")
        return False

def create_test_user_and_verify():
    """Create a test user and verify their email"""
    print("\n🧪 Creating test user and verifying email...")
    
    # Import User model to create test user
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        from app.models.User import User
        import secrets
        from datetime import datetime, timedelta
        
        # Create test user
        email = f"test{int(time.time())}@verification.test"
        user = User()
        user.name = "Test Verification User"
        user.first_name = "Test"
        user.last_name = "User"
        user.email = email
        user.set_password("TestPass123!")
        token = secrets.token_urlsafe(32)
        user.email_verification_token = token
        user.email_verification_expires = datetime.now() + timedelta(hours=24)
        user.save()
        
        print(f"✅ Created test user: {email}")
        print(f"✅ Verification token: {token[:20]}...")
        
        # Test verification
        headers = {
            "Content-Type": "application/json",
            "Origin": FRONTEND_ORIGIN
        }
        
        data = {"token": token}
        
        response = requests.post(f"{API_BASE}/auth/verify-email", json=data, headers=headers, timeout=5)
        print(f"✅ Verification status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Verification successful!")
            print(f"   Message: {result.get('message')}")
            print(f"   User ID: {result.get('user', {}).get('id')}")
            print(f"   Email Verified: {result.get('user', {}).get('emailVerified')}")
            return True
        else:
            print(f"❌ Verification failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test user creation/verification failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Email Verification Fix")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: CORS preflight
    if test_cors_preflight():
        tests_passed += 1
    
    # Test 2: Invalid token
    if test_verify_email_invalid_token():
        tests_passed += 1
    
    # Test 3: Valid token verification
    if create_test_user_and_verify():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"🎯 Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Email verification should work with frontend.")
        print("\n📋 Summary of fixes applied:")
        print("  ✅ Backend running on correct port (3002)")
        print("  ✅ Fixed User.mark_email_as_verified() method")
        print("  ✅ CORS middleware properly configured")
        print("  ✅ Email verification endpoint working correctly")
        print("\n🔗 Frontend should now be able to:")
        print("  1. Make OPTIONS preflight requests successfully")
        print("  2. Make POST requests to /api/auth/verify-email")
        print("  3. Receive proper JSON responses with user data")
        print("\n💡 The Angular error might be resolved by the backend fixes.")
        print("   If it persists, it's likely a frontend timing issue that can")
        print("   be fixed by using async/await properly in the component.")
    else:
        print("❌ Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
