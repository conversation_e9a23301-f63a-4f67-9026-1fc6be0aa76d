{"ast": null, "code": "import * as i1 from '@angular/common/http';\nimport { HttpClient } from '@angular/common/http';\nimport * as i0 from '@angular/core';\nimport { SecurityContext, DOCUMENT, Injectable, Optional, Inject, SkipSelf, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport * as i2 from '@angular/platform-browser';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport { of, throwError, forkJoin } from 'rxjs';\nimport { tap, map, catchError, finalize, share } from 'rxjs/operators';\n\n/**\n * The Trusted Types policy, or null if Trusted Types are not\n * enabled/supported, or undefined if the policy has not been created yet.\n */\nlet policy;\n/**\n * Returns the Trusted Types policy, or null if Trusted Types are not\n * enabled/supported. The first call to this function will create the policy.\n */\nfunction getPolicy() {\n  if (policy === undefined) {\n    policy = null;\n    if (typeof window !== 'undefined') {\n      const ttWindow = window;\n      if (ttWindow.trustedTypes !== undefined) {\n        policy = ttWindow.trustedTypes.createPolicy('angular#components', {\n          createHTML: s => s\n        });\n      }\n    }\n  }\n  return policy;\n}\n/**\n * Unsafely promote a string to a TrustedHTML, falling back to strings when\n * Trusted Types are not available.\n * @security This is a security-sensitive function; any use of this function\n * must go through security review. In particular, it must be assured that the\n * provided string will never cause an XSS vulnerability if used in a context\n * that will be interpreted as HTML by a browser, e.g. when assigning to\n * element.innerHTML.\n */\nfunction trustedHTMLFromString(html) {\n  return getPolicy()?.createHTML(html) || html;\n}\n\n/**\n * Returns an exception to be thrown in the case when attempting to\n * load an icon with a name that cannot be found.\n * @docs-private\n */\nfunction getMatIconNameNotFoundError(iconName) {\n  return Error(`Unable to find icon with the name \"${iconName}\"`);\n}\n/**\n * Returns an exception to be thrown when the consumer attempts to use\n * `<mat-icon>` without including @angular/common/http.\n * @docs-private\n */\nfunction getMatIconNoHttpProviderError() {\n  return Error('Could not find HttpClient for use with Angular Material icons. ' + 'Please add provideHttpClient() to your providers.');\n}\n/**\n * Returns an exception to be thrown when a URL couldn't be sanitized.\n * @param url URL that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeUrlError(url) {\n  return Error(`The URL provided to MatIconRegistry was not trusted as a resource URL ` + `via Angular's DomSanitizer. Attempted URL was \"${url}\".`);\n}\n/**\n * Returns an exception to be thrown when a HTML string couldn't be sanitized.\n * @param literal HTML that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeLiteralError(literal) {\n  return Error(`The literal provided to MatIconRegistry was not trusted as safe HTML by ` + `Angular's DomSanitizer. Attempted literal was \"${literal}\".`);\n}\n/**\n * Configuration for an icon, including the URL and possibly the cached SVG element.\n * @docs-private\n */\nclass SvgIconConfig {\n  url;\n  svgText;\n  options;\n  svgElement;\n  constructor(url, svgText, options) {\n    this.url = url;\n    this.svgText = svgText;\n    this.options = options;\n  }\n}\n/**\n * Service to register and display icons used by the `<mat-icon>` component.\n * - Registers icon URLs by namespace and name.\n * - Registers icon set URLs by namespace.\n * - Registers aliases for CSS classes, for use with icon fonts.\n * - Loads icons from URLs and extracts individual icons from icon sets.\n */\nlet MatIconRegistry = /*#__PURE__*/(() => {\n  class MatIconRegistry {\n    _httpClient;\n    _sanitizer;\n    _errorHandler;\n    _document;\n    /**\n     * URLs and cached SVG elements for individual icons. Keys are of the format \"[namespace]:[icon]\".\n     */\n    _svgIconConfigs = new Map();\n    /**\n     * SvgIconConfig objects and cached SVG elements for icon sets, keyed by namespace.\n     * Multiple icon sets can be registered under the same namespace.\n     */\n    _iconSetConfigs = new Map();\n    /** Cache for icons loaded by direct URLs. */\n    _cachedIconsByUrl = new Map();\n    /** In-progress icon fetches. Used to coalesce multiple requests to the same URL. */\n    _inProgressUrlFetches = new Map();\n    /** Map from font identifiers to their CSS class names. Used for icon fonts. */\n    _fontCssClassesByAlias = new Map();\n    /** Registered icon resolver functions. */\n    _resolvers = [];\n    /**\n     * The CSS classes to apply when an `<mat-icon>` component has no icon name, url, or font\n     * specified. The default 'material-icons' value assumes that the material icon font has been\n     * loaded as described at https://google.github.io/material-design-icons/#icon-font-for-the-web\n     */\n    _defaultFontSetClass = ['material-icons', 'mat-ligature-font'];\n    constructor(_httpClient, _sanitizer, document, _errorHandler) {\n      this._httpClient = _httpClient;\n      this._sanitizer = _sanitizer;\n      this._errorHandler = _errorHandler;\n      this._document = document;\n    }\n    /**\n     * Registers an icon by URL in the default namespace.\n     * @param iconName Name under which the icon should be registered.\n     * @param url\n     */\n    addSvgIcon(iconName, url, options) {\n      return this.addSvgIconInNamespace('', iconName, url, options);\n    }\n    /**\n     * Registers an icon using an HTML string in the default namespace.\n     * @param iconName Name under which the icon should be registered.\n     * @param literal SVG source of the icon.\n     */\n    addSvgIconLiteral(iconName, literal, options) {\n      return this.addSvgIconLiteralInNamespace('', iconName, literal, options);\n    }\n    /**\n     * Registers an icon by URL in the specified namespace.\n     * @param namespace Namespace in which the icon should be registered.\n     * @param iconName Name under which the icon should be registered.\n     * @param url\n     */\n    addSvgIconInNamespace(namespace, iconName, url, options) {\n      return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig(url, null, options));\n    }\n    /**\n     * Registers an icon resolver function with the registry. The function will be invoked with the\n     * name and namespace of an icon when the registry tries to resolve the URL from which to fetch\n     * the icon. The resolver is expected to return a `SafeResourceUrl` that points to the icon,\n     * an object with the icon URL and icon options, or `null` if the icon is not supported. Resolvers\n     * will be invoked in the order in which they have been registered.\n     * @param resolver Resolver function to be registered.\n     */\n    addSvgIconResolver(resolver) {\n      this._resolvers.push(resolver);\n      return this;\n    }\n    /**\n     * Registers an icon using an HTML string in the specified namespace.\n     * @param namespace Namespace in which the icon should be registered.\n     * @param iconName Name under which the icon should be registered.\n     * @param literal SVG source of the icon.\n     */\n    addSvgIconLiteralInNamespace(namespace, iconName, literal, options) {\n      const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n      // TODO: add an ngDevMode check\n      if (!cleanLiteral) {\n        throw getMatIconFailedToSanitizeLiteralError(literal);\n      }\n      // Security: The literal is passed in as SafeHtml, and is thus trusted.\n      const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n      return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig('', trustedLiteral, options));\n    }\n    /**\n     * Registers an icon set by URL in the default namespace.\n     * @param url\n     */\n    addSvgIconSet(url, options) {\n      return this.addSvgIconSetInNamespace('', url, options);\n    }\n    /**\n     * Registers an icon set using an HTML string in the default namespace.\n     * @param literal SVG source of the icon set.\n     */\n    addSvgIconSetLiteral(literal, options) {\n      return this.addSvgIconSetLiteralInNamespace('', literal, options);\n    }\n    /**\n     * Registers an icon set by URL in the specified namespace.\n     * @param namespace Namespace in which to register the icon set.\n     * @param url\n     */\n    addSvgIconSetInNamespace(namespace, url, options) {\n      return this._addSvgIconSetConfig(namespace, new SvgIconConfig(url, null, options));\n    }\n    /**\n     * Registers an icon set using an HTML string in the specified namespace.\n     * @param namespace Namespace in which to register the icon set.\n     * @param literal SVG source of the icon set.\n     */\n    addSvgIconSetLiteralInNamespace(namespace, literal, options) {\n      const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n      if (!cleanLiteral) {\n        throw getMatIconFailedToSanitizeLiteralError(literal);\n      }\n      // Security: The literal is passed in as SafeHtml, and is thus trusted.\n      const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n      return this._addSvgIconSetConfig(namespace, new SvgIconConfig('', trustedLiteral, options));\n    }\n    /**\n     * Defines an alias for CSS class names to be used for icon fonts. Creating an matIcon\n     * component with the alias as the fontSet input will cause the class name to be applied\n     * to the `<mat-icon>` element.\n     *\n     * If the registered font is a ligature font, then don't forget to also include the special\n     * class `mat-ligature-font` to allow the usage via attribute. So register like this:\n     *\n     * ```ts\n     * iconRegistry.registerFontClassAlias('f1', 'font1 mat-ligature-font');\n     * ```\n     *\n     * And use like this:\n     *\n     * ```html\n     * <mat-icon fontSet=\"f1\" fontIcon=\"home\"></mat-icon>\n     * ```\n     *\n     * @param alias Alias for the font.\n     * @param classNames Class names override to be used instead of the alias.\n     */\n    registerFontClassAlias(alias, classNames = alias) {\n      this._fontCssClassesByAlias.set(alias, classNames);\n      return this;\n    }\n    /**\n     * Returns the CSS class name associated with the alias by a previous call to\n     * registerFontClassAlias. If no CSS class has been associated, returns the alias unmodified.\n     */\n    classNameForFontAlias(alias) {\n      return this._fontCssClassesByAlias.get(alias) || alias;\n    }\n    /**\n     * Sets the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n     * have a fontSet input value, and is not loading an icon by name or URL.\n     */\n    setDefaultFontSetClass(...classNames) {\n      this._defaultFontSetClass = classNames;\n      return this;\n    }\n    /**\n     * Returns the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n     * have a fontSet input value, and is not loading an icon by name or URL.\n     */\n    getDefaultFontSetClass() {\n      return this._defaultFontSetClass;\n    }\n    /**\n     * Returns an Observable that produces the icon (as an `<svg>` DOM element) from the given URL.\n     * The response from the URL may be cached so this will not always cause an HTTP request, but\n     * the produced element will always be a new copy of the originally fetched icon. (That is,\n     * it will not contain any modifications made to elements previously returned).\n     *\n     * @param safeUrl URL from which to fetch the SVG icon.\n     */\n    getSvgIconFromUrl(safeUrl) {\n      const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n      if (!url) {\n        throw getMatIconFailedToSanitizeUrlError(safeUrl);\n      }\n      const cachedIcon = this._cachedIconsByUrl.get(url);\n      if (cachedIcon) {\n        return of(cloneSvg(cachedIcon));\n      }\n      return this._loadSvgIconFromConfig(new SvgIconConfig(safeUrl, null)).pipe(tap(svg => this._cachedIconsByUrl.set(url, svg)), map(svg => cloneSvg(svg)));\n    }\n    /**\n     * Returns an Observable that produces the icon (as an `<svg>` DOM element) with the given name\n     * and namespace. The icon must have been previously registered with addIcon or addIconSet;\n     * if not, the Observable will throw an error.\n     *\n     * @param name Name of the icon to be retrieved.\n     * @param namespace Namespace in which to look for the icon.\n     */\n    getNamedSvgIcon(name, namespace = '') {\n      const key = iconKey(namespace, name);\n      let config = this._svgIconConfigs.get(key);\n      // Return (copy of) cached icon if possible.\n      if (config) {\n        return this._getSvgFromConfig(config);\n      }\n      // Otherwise try to resolve the config from one of the resolver functions.\n      config = this._getIconConfigFromResolvers(namespace, name);\n      if (config) {\n        this._svgIconConfigs.set(key, config);\n        return this._getSvgFromConfig(config);\n      }\n      // See if we have any icon sets registered for the namespace.\n      const iconSetConfigs = this._iconSetConfigs.get(namespace);\n      if (iconSetConfigs) {\n        return this._getSvgFromIconSetConfigs(name, iconSetConfigs);\n      }\n      return throwError(getMatIconNameNotFoundError(key));\n    }\n    ngOnDestroy() {\n      this._resolvers = [];\n      this._svgIconConfigs.clear();\n      this._iconSetConfigs.clear();\n      this._cachedIconsByUrl.clear();\n    }\n    /**\n     * Returns the cached icon for a SvgIconConfig if available, or fetches it from its URL if not.\n     */\n    _getSvgFromConfig(config) {\n      if (config.svgText) {\n        // We already have the SVG element for this icon, return a copy.\n        return of(cloneSvg(this._svgElementFromConfig(config)));\n      } else {\n        // Fetch the icon from the config's URL, cache it, and return a copy.\n        return this._loadSvgIconFromConfig(config).pipe(map(svg => cloneSvg(svg)));\n      }\n    }\n    /**\n     * Attempts to find an icon with the specified name in any of the SVG icon sets.\n     * First searches the available cached icons for a nested element with a matching name, and\n     * if found copies the element to a new `<svg>` element. If not found, fetches all icon sets\n     * that have not been cached, and searches again after all fetches are completed.\n     * The returned Observable produces the SVG element if possible, and throws\n     * an error if no icon with the specified name can be found.\n     */\n    _getSvgFromIconSetConfigs(name, iconSetConfigs) {\n      // For all the icon set SVG elements we've fetched, see if any contain an icon with the\n      // requested name.\n      const namedIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n      if (namedIcon) {\n        // We could cache namedIcon in _svgIconConfigs, but since we have to make a copy every\n        // time anyway, there's probably not much advantage compared to just always extracting\n        // it from the icon set.\n        return of(namedIcon);\n      }\n      // Not found in any cached icon sets. If there are icon sets with URLs that we haven't\n      // fetched, fetch them now and look for iconName in the results.\n      const iconSetFetchRequests = iconSetConfigs.filter(iconSetConfig => !iconSetConfig.svgText).map(iconSetConfig => {\n        return this._loadSvgIconSetFromConfig(iconSetConfig).pipe(catchError(err => {\n          const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, iconSetConfig.url);\n          // Swallow errors fetching individual URLs so the\n          // combined Observable won't necessarily fail.\n          const errorMessage = `Loading icon set URL: ${url} failed: ${err.message}`;\n          this._errorHandler.handleError(new Error(errorMessage));\n          return of(null);\n        }));\n      });\n      // Fetch all the icon set URLs. When the requests complete, every IconSet should have a\n      // cached SVG element (unless the request failed), and we can check again for the icon.\n      return forkJoin(iconSetFetchRequests).pipe(map(() => {\n        const foundIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n        // TODO: add an ngDevMode check\n        if (!foundIcon) {\n          throw getMatIconNameNotFoundError(name);\n        }\n        return foundIcon;\n      }));\n    }\n    /**\n     * Searches the cached SVG elements for the given icon sets for a nested icon element whose \"id\"\n     * tag matches the specified name. If found, copies the nested element to a new SVG element and\n     * returns it. Returns null if no matching element is found.\n     */\n    _extractIconWithNameFromAnySet(iconName, iconSetConfigs) {\n      // Iterate backwards, so icon sets added later have precedence.\n      for (let i = iconSetConfigs.length - 1; i >= 0; i--) {\n        const config = iconSetConfigs[i];\n        // Parsing the icon set's text into an SVG element can be expensive. We can avoid some of\n        // the parsing by doing a quick check using `indexOf` to see if there's any chance for the\n        // icon to be in the set. This won't be 100% accurate, but it should help us avoid at least\n        // some of the parsing.\n        if (config.svgText && config.svgText.toString().indexOf(iconName) > -1) {\n          const svg = this._svgElementFromConfig(config);\n          const foundIcon = this._extractSvgIconFromSet(svg, iconName, config.options);\n          if (foundIcon) {\n            return foundIcon;\n          }\n        }\n      }\n      return null;\n    }\n    /**\n     * Loads the content of the icon URL specified in the SvgIconConfig and creates an SVG element\n     * from it.\n     */\n    _loadSvgIconFromConfig(config) {\n      return this._fetchIcon(config).pipe(tap(svgText => config.svgText = svgText), map(() => this._svgElementFromConfig(config)));\n    }\n    /**\n     * Loads the content of the icon set URL specified in the\n     * SvgIconConfig and attaches it to the config.\n     */\n    _loadSvgIconSetFromConfig(config) {\n      if (config.svgText) {\n        return of(null);\n      }\n      return this._fetchIcon(config).pipe(tap(svgText => config.svgText = svgText));\n    }\n    /**\n     * Searches the cached element of the given SvgIconConfig for a nested icon element whose \"id\"\n     * tag matches the specified name. If found, copies the nested element to a new SVG element and\n     * returns it. Returns null if no matching element is found.\n     */\n    _extractSvgIconFromSet(iconSet, iconName, options) {\n      // Use the `id=\"iconName\"` syntax in order to escape special\n      // characters in the ID (versus using the #iconName syntax).\n      const iconSource = iconSet.querySelector(`[id=\"${iconName}\"]`);\n      if (!iconSource) {\n        return null;\n      }\n      // Clone the element and remove the ID to prevent multiple elements from being added\n      // to the page with the same ID.\n      const iconElement = iconSource.cloneNode(true);\n      iconElement.removeAttribute('id');\n      // If the icon node is itself an <svg> node, clone and return it directly. If not, set it as\n      // the content of a new <svg> node.\n      if (iconElement.nodeName.toLowerCase() === 'svg') {\n        return this._setSvgAttributes(iconElement, options);\n      }\n      // If the node is a <symbol>, it won't be rendered so we have to convert it into <svg>. Note\n      // that the same could be achieved by referring to it via <use href=\"#id\">, however the <use>\n      // tag is problematic on Firefox, because it needs to include the current page path.\n      if (iconElement.nodeName.toLowerCase() === 'symbol') {\n        return this._setSvgAttributes(this._toSvgElement(iconElement), options);\n      }\n      // createElement('SVG') doesn't work as expected; the DOM ends up with\n      // the correct nodes, but the SVG content doesn't render. Instead we\n      // have to create an empty SVG node using innerHTML and append its content.\n      // Elements created using DOMParser.parseFromString have the same problem.\n      // http://stackoverflow.com/questions/23003278/svg-innerhtml-in-firefox-can-not-display\n      const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n      // Clone the node so we don't remove it from the parent icon set element.\n      svg.appendChild(iconElement);\n      return this._setSvgAttributes(svg, options);\n    }\n    /**\n     * Creates a DOM element from the given SVG string.\n     */\n    _svgElementFromString(str) {\n      const div = this._document.createElement('DIV');\n      div.innerHTML = str;\n      const svg = div.querySelector('svg');\n      // TODO: add an ngDevMode check\n      if (!svg) {\n        throw Error('<svg> tag not found');\n      }\n      return svg;\n    }\n    /**\n     * Converts an element into an SVG node by cloning all of its children.\n     */\n    _toSvgElement(element) {\n      const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n      const attributes = element.attributes;\n      // Copy over all the attributes from the `symbol` to the new SVG, except the id.\n      for (let i = 0; i < attributes.length; i++) {\n        const {\n          name,\n          value\n        } = attributes[i];\n        if (name !== 'id') {\n          svg.setAttribute(name, value);\n        }\n      }\n      for (let i = 0; i < element.childNodes.length; i++) {\n        if (element.childNodes[i].nodeType === this._document.ELEMENT_NODE) {\n          svg.appendChild(element.childNodes[i].cloneNode(true));\n        }\n      }\n      return svg;\n    }\n    /**\n     * Sets the default attributes for an SVG element to be used as an icon.\n     */\n    _setSvgAttributes(svg, options) {\n      svg.setAttribute('fit', '');\n      svg.setAttribute('height', '100%');\n      svg.setAttribute('width', '100%');\n      svg.setAttribute('preserveAspectRatio', 'xMidYMid meet');\n      svg.setAttribute('focusable', 'false'); // Disable IE11 default behavior to make SVGs focusable.\n      if (options && options.viewBox) {\n        svg.setAttribute('viewBox', options.viewBox);\n      }\n      return svg;\n    }\n    /**\n     * Returns an Observable which produces the string contents of the given icon. Results may be\n     * cached, so future calls with the same URL may not cause another HTTP request.\n     */\n    _fetchIcon(iconConfig) {\n      const {\n        url: safeUrl,\n        options\n      } = iconConfig;\n      const withCredentials = options?.withCredentials ?? false;\n      if (!this._httpClient) {\n        throw getMatIconNoHttpProviderError();\n      }\n      // TODO: add an ngDevMode check\n      if (safeUrl == null) {\n        throw Error(`Cannot fetch icon from URL \"${safeUrl}\".`);\n      }\n      const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n      // TODO: add an ngDevMode check\n      if (!url) {\n        throw getMatIconFailedToSanitizeUrlError(safeUrl);\n      }\n      // Store in-progress fetches to avoid sending a duplicate request for a URL when there is\n      // already a request in progress for that URL. It's necessary to call share() on the\n      // Observable returned by http.get() so that multiple subscribers don't cause multiple XHRs.\n      const inProgressFetch = this._inProgressUrlFetches.get(url);\n      if (inProgressFetch) {\n        return inProgressFetch;\n      }\n      const req = this._httpClient.get(url, {\n        responseType: 'text',\n        withCredentials\n      }).pipe(map(svg => {\n        // Security: This SVG is fetched from a SafeResourceUrl, and is thus\n        // trusted HTML.\n        return trustedHTMLFromString(svg);\n      }), finalize(() => this._inProgressUrlFetches.delete(url)), share());\n      this._inProgressUrlFetches.set(url, req);\n      return req;\n    }\n    /**\n     * Registers an icon config by name in the specified namespace.\n     * @param namespace Namespace in which to register the icon config.\n     * @param iconName Name under which to register the config.\n     * @param config Config to be registered.\n     */\n    _addSvgIconConfig(namespace, iconName, config) {\n      this._svgIconConfigs.set(iconKey(namespace, iconName), config);\n      return this;\n    }\n    /**\n     * Registers an icon set config in the specified namespace.\n     * @param namespace Namespace in which to register the icon config.\n     * @param config Config to be registered.\n     */\n    _addSvgIconSetConfig(namespace, config) {\n      const configNamespace = this._iconSetConfigs.get(namespace);\n      if (configNamespace) {\n        configNamespace.push(config);\n      } else {\n        this._iconSetConfigs.set(namespace, [config]);\n      }\n      return this;\n    }\n    /** Parses a config's text into an SVG element. */\n    _svgElementFromConfig(config) {\n      if (!config.svgElement) {\n        const svg = this._svgElementFromString(config.svgText);\n        this._setSvgAttributes(svg, config.options);\n        config.svgElement = svg;\n      }\n      return config.svgElement;\n    }\n    /** Tries to create an icon config through the registered resolver functions. */\n    _getIconConfigFromResolvers(namespace, name) {\n      for (let i = 0; i < this._resolvers.length; i++) {\n        const result = this._resolvers[i](name, namespace);\n        if (result) {\n          return isSafeUrlWithOptions(result) ? new SvgIconConfig(result.url, null, result.options) : new SvgIconConfig(result, null);\n        }\n      }\n      return undefined;\n    }\n    static ɵfac = function MatIconRegistry_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatIconRegistry)(i0.ɵɵinject(i1.HttpClient, 8), i0.ɵɵinject(i2.DomSanitizer), i0.ɵɵinject(DOCUMENT, 8), i0.ɵɵinject(i0.ErrorHandler));\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatIconRegistry,\n      factory: MatIconRegistry.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return MatIconRegistry;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nfunction ICON_REGISTRY_PROVIDER_FACTORY(parentRegistry, httpClient, sanitizer, errorHandler, document) {\n  return parentRegistry || new MatIconRegistry(httpClient, sanitizer, document, errorHandler);\n}\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst ICON_REGISTRY_PROVIDER = {\n  // If there is already an MatIconRegistry available, use that. Otherwise, provide a new one.\n  provide: MatIconRegistry,\n  deps: [[/*#__PURE__*/new Optional(), /*#__PURE__*/new SkipSelf(), MatIconRegistry], [/*#__PURE__*/new Optional(), HttpClient], DomSanitizer, ErrorHandler, [/*#__PURE__*/new Optional(), DOCUMENT]],\n  useFactory: ICON_REGISTRY_PROVIDER_FACTORY\n};\n/** Clones an SVGElement while preserving type information. */\nfunction cloneSvg(svg) {\n  return svg.cloneNode(true);\n}\n/** Returns the cache key to use for an icon namespace and name. */\nfunction iconKey(namespace, name) {\n  return namespace + ':' + name;\n}\nfunction isSafeUrlWithOptions(value) {\n  return !!(value.url && value.options);\n}\nexport { ICON_REGISTRY_PROVIDER_FACTORY as I, MatIconRegistry as M, getMatIconNoHttpProviderError as a, getMatIconFailedToSanitizeUrlError as b, getMatIconFailedToSanitizeLiteralError as c, ICON_REGISTRY_PROVIDER as d, getMatIconNameNotFoundError as g };\n//# sourceMappingURL=icon-registry-CwOTJ7YM.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}