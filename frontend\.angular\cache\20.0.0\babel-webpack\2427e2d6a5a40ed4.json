{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Directive, NgModule } from '@angular/core';\nimport { startWith } from 'rxjs/operators';\nimport { M as MatCommonModule } from './common-module-cKSwHniA.mjs';\n\n/**\n * Shared directive to count lines inside a text area, such as a list item.\n * Line elements can be extracted with a @ContentChildren(MatLine) query, then\n * counted by checking the query list's length.\n */\nlet MatLine = /*#__PURE__*/(() => {\n  class MatLine {\n    static ɵfac = function MatLine_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatLine)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatLine,\n      selectors: [[\"\", \"mat-line\", \"\"], [\"\", \"matLine\", \"\"]],\n      hostAttrs: [1, \"mat-line\"]\n    });\n  }\n  return MatLine;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Helper that takes a query list of lines and sets the correct class on the host.\n * @docs-private\n */\nfunction setLines(lines, element, prefix = 'mat') {\n  // Note: doesn't need to unsubscribe, because `changes`\n  // gets completed by Angular when the view is destroyed.\n  lines.changes.pipe(startWith(lines)).subscribe(({\n    length\n  }) => {\n    setClass(element, `${prefix}-2-line`, false);\n    setClass(element, `${prefix}-3-line`, false);\n    setClass(element, `${prefix}-multi-line`, false);\n    if (length === 2 || length === 3) {\n      setClass(element, `${prefix}-${length}-line`, true);\n    } else if (length > 3) {\n      setClass(element, `${prefix}-multi-line`, true);\n    }\n  });\n}\n/** Adds or removes a class from an element. */\nfunction setClass(element, className, isAdd) {\n  element.nativeElement.classList.toggle(className, isAdd);\n}\nlet MatLineModule = /*#__PURE__*/(() => {\n  class MatLineModule {\n    static ɵfac = function MatLineModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatLineModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatLineModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatCommonModule]\n    });\n  }\n  return MatLineModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nexport { MatLine as M, MatLineModule as a, setLines as s };\n//# sourceMappingURL=line-Bz5f9Cyx.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}