{"ast": null, "code": "import { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { timer } from '../observable/timer';\nexport function repeat(countOrConfig) {\n  let count = Infinity;\n  let delay;\n  if (countOrConfig != null) {\n    if (typeof countOrConfig === 'object') {\n      ({\n        count = Infinity,\n        delay\n      } = countOrConfig);\n    } else {\n      count = countOrConfig;\n    }\n  }\n  return count <= 0 ? () => EMPTY : operate((source, subscriber) => {\n    let soFar = 0;\n    let sourceSub;\n    const resubscribe = () => {\n      sourceSub === null || sourceSub === void 0 ? void 0 : sourceSub.unsubscribe();\n      sourceSub = null;\n      if (delay != null) {\n        const notifier = typeof delay === 'number' ? timer(delay) : innerFrom(delay(soFar));\n        const notifierSubscriber = createOperatorSubscriber(subscriber, () => {\n          notifierSubscriber.unsubscribe();\n          subscribeToSource();\n        });\n        notifier.subscribe(notifierSubscriber);\n      } else {\n        subscribeToSource();\n      }\n    };\n    const subscribeToSource = () => {\n      let syncUnsub = false;\n      sourceSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, () => {\n        if (++soFar < count) {\n          if (sourceSub) {\n            resubscribe();\n          } else {\n            syncUnsub = true;\n          }\n        } else {\n          subscriber.complete();\n        }\n      }));\n      if (syncUnsub) {\n        resubscribe();\n      }\n    };\n    subscribeToSource();\n  });\n}\n//# sourceMappingURL=repeat.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}