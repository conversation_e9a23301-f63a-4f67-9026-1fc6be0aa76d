{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nfunction ForgotPasswordComponent_div_10_mat_error_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Email is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ForgotPasswordComponent_div_10_mat_error_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Please enter a valid email address \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ForgotPasswordComponent_div_10_mat_spinner_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"diameter\", 20);\n  }\n}\nfunction ForgotPasswordComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"form\", 5);\n    i0.ɵɵlistener(\"ngSubmit\", function ForgotPasswordComponent_div_10_Template_form_ngSubmit_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementStart(2, \"mat-form-field\", 6)(3, \"mat-label\");\n    i0.ɵɵtext(4, \"Email Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 7);\n    i0.ɵɵelementStart(6, \"mat-icon\", 8);\n    i0.ɵɵtext(7, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ForgotPasswordComponent_div_10_mat_error_8_Template, 2, 0, \"mat-error\", 2)(9, ForgotPasswordComponent_div_10_mat_error_9_Template, 2, 0, \"mat-error\", 2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 9);\n    i0.ɵɵtemplate(11, ForgotPasswordComponent_div_10_mat_spinner_11_Template, 1, 1, \"mat-spinner\", 10);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.forgotPasswordForm);\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_2_0 = ctx_r1.forgotPasswordForm.get(\"email\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r1.forgotPasswordForm.get(\"email\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r1.forgotPasswordForm.get(\"email\")) == null ? null : tmp_3_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r1.forgotPasswordForm.get(\"email\")) == null ? null : tmp_4_0.hasError(\"email\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.forgotPasswordForm.invalid || ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.isLoading ? \"Sending...\" : \"Send Reset Instructions\", \" \");\n  }\n}\nfunction ForgotPasswordComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"mat-icon\", 13);\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Check Your Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \" We've sent password reset instructions to your email address. Please check your inbox and follow the link to reset your password. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 14)(8, \"strong\");\n    i0.ɵɵtext(9, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" If you don't see the email in your inbox, please check your spam or junk folder. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let ForgotPasswordComponent = /*#__PURE__*/(() => {\n  class ForgotPasswordComponent {\n    constructor(fb, authService, router, snackBar) {\n      this.fb = fb;\n      this.authService = authService;\n      this.router = router;\n      this.snackBar = snackBar;\n      this.isLoading = false;\n      this.isEmailSent = false;\n    }\n    ngOnInit() {\n      this.forgotPasswordForm = this.fb.group({\n        email: ['', [Validators.required, Validators.email]]\n      });\n    }\n    onSubmit() {\n      if (this.forgotPasswordForm.valid && !this.isLoading) {\n        this.isLoading = true;\n        const email = this.forgotPasswordForm.get('email')?.value;\n        this.authService.forgotPassword(email).subscribe({\n          next: response => {\n            this.isLoading = false;\n            this.isEmailSent = true;\n            this.snackBar.open('Password reset instructions have been sent to your email address', 'Close', {\n              duration: 5000,\n              panelClass: ['success-snackbar']\n            });\n          },\n          error: error => {\n            this.isLoading = false;\n            const errorMessage = error?.error?.message || 'Failed to send reset email. Please try again.';\n            this.snackBar.open(errorMessage, 'Close', {\n              duration: 5000,\n              panelClass: ['error-snackbar']\n            });\n          }\n        });\n      }\n    }\n    goBackToLogin() {\n      this.router.navigate(['/auth/login']);\n    }\n    static #_ = this.ɵfac = function ForgotPasswordComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ForgotPasswordComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ForgotPasswordComponent,\n      selectors: [[\"app-forgot-password\"]],\n      standalone: false,\n      decls: 17,\n      vars: 2,\n      consts: [[1, \"auth-container\"], [1, \"auth-card\"], [4, \"ngIf\"], [\"class\", \"success-message\", 4, \"ngIf\"], [\"mat-button\", \"\", \"color\", \"accent\", 1, \"full-width\", 3, \"click\"], [1, \"auth-form\", 3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email address\"], [\"matSuffix\", \"\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"submit-btn\", 3, \"disabled\"], [\"style\", \"display: inline-block; margin-right: 8px;\", 3, \"diameter\", 4, \"ngIf\"], [2, \"display\", \"inline-block\", \"margin-right\", \"8px\", 3, \"diameter\"], [1, \"success-message\"], [1, \"success-icon\"], [1, \"note\"]],\n      template: function ForgotPasswordComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n          i0.ɵɵtext(5, \"lock_reset\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Forgot Password \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n          i0.ɵɵtext(8, \" Enter your email address to receive password reset instructions \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"mat-card-content\");\n          i0.ɵɵtemplate(10, ForgotPasswordComponent_div_10_Template, 13, 8, \"div\", 2)(11, ForgotPasswordComponent_div_11_Template, 11, 0, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"mat-card-actions\")(13, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function ForgotPasswordComponent_Template_button_click_13_listener() {\n            return ctx.goBackToLogin();\n          });\n          i0.ɵɵelementStart(14, \"mat-icon\");\n          i0.ɵɵtext(15, \"arrow_back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(16, \" Back to Login \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isEmailSent);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isEmailSent);\n        }\n      },\n      dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.MatCard, i6.MatCardActions, i6.MatCardContent, i6.MatCardHeader, i6.MatCardSubtitle, i6.MatCardTitle, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, i8.MatInput, i9.MatButton, i10.MatIcon, i11.MatProgressSpinner],\n      styles: [\".auth-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;padding:20px;background:linear-gradient(135deg,#667eea,#764ba2)}.auth-card[_ngcontent-%COMP%]{width:100%;max-width:450px;border-radius:12px;box-shadow:0 10px 30px #0000001a}.auth-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px;margin-top:20px}.full-width[_ngcontent-%COMP%]{width:100%}.submit-btn[_ngcontent-%COMP%]{height:48px;font-size:16px;font-weight:500;margin-top:10px}.success-message[_ngcontent-%COMP%]{text-align:center;padding:20px 0}.success-message[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%]{font-size:48px;height:48px;width:48px;color:#4caf50;margin-bottom:16px}.success-message[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#333;margin-bottom:16px;font-weight:500}.success-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;line-height:1.5;margin-bottom:12px}.success-message[_ngcontent-%COMP%]   .note[_ngcontent-%COMP%]{font-size:14px;background-color:#f5f5f5;padding:12px;border-radius:6px;border-left:4px solid #2196f3}mat-card-header[_ngcontent-%COMP%]{text-align:center;padding-bottom:0}mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:8px;font-size:24px;color:#333}mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:28px;height:28px;width:28px}mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%]{color:#666;margin-top:8px;font-size:14px}mat-card-actions[_ngcontent-%COMP%]{padding-top:0}.is-invalid[_ngcontent-%COMP%]{border-color:#f44336}@media (max-width: 600px){.auth-container[_ngcontent-%COMP%]{padding:10px}.auth-card[_ngcontent-%COMP%]{max-width:100%}}\"]\n    });\n  }\n  return ForgotPasswordComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}