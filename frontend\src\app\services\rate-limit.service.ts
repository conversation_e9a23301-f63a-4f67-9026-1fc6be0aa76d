import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, timer, EMPTY } from 'rxjs';
import { switchMap, takeWhile, tap, finalize } from 'rxjs/operators';

export interface RateLimitStatus {
  isRateLimited: boolean;
  retryAfter: number;
  remainingTime: number;
  message: string;
  limit: number;
  remaining: number;
  resetTime: number;
}

@Injectable({
  providedIn: 'root'
})
export class RateLimitService {
  private readonly STORAGE_KEY = 'rateLimit_status';
  private rateLimitSubject = new BehaviorSubject<RateLimitStatus>(this.getInitialStatus());
  
  public rateLimit$ = this.rateLimitSubject.asObservable();
  private countdownSubscription?: any;
  constructor() {
    // Clear any corrupted localStorage data first
    this.clearCorruptedData();
    // Restore any valid active rate limit from localStorage on service init
    this.restoreFromStorage();
  }

  /**
   * Clear corrupted or invalid data from localStorage
   */
  private clearCorruptedData(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const data = JSON.parse(stored);
        // Check for corrupted data (extremely large values indicate corruption)
        if (data.remainingTime > 3600 || data.retryAfter > 3600 || data.resetTime > Date.now() / 1000 + 3600) {
          console.warn('🧹 Clearing corrupted rate limit data from localStorage');
          localStorage.removeItem(this.STORAGE_KEY);
        }
      }
    } catch {
      // Clear if JSON parsing fails
      localStorage.removeItem(this.STORAGE_KEY);
    }
  }

  /**
   * Get initial status (either from localStorage or default)
   */
  private getInitialStatus(): RateLimitStatus {
    const stored = this.getStoredStatus();
    if (stored && stored.isRateLimited && stored.remainingTime > 0) {
      return stored;
    }
    
    return {
      isRateLimited: false,
      retryAfter: 0,
      remainingTime: 0,
      message: '',
      limit: 0,
      remaining: 0,
      resetTime: 0
    };
  }  /**
   * Restore rate limit status from localStorage and continue countdown if needed
   */
  private restoreFromStorage(): void {
    const stored = this.getStoredStatus();
    if (!stored || !stored.isRateLimited) return;

    const now = Math.floor(Date.now() / 1000); // Current time in seconds
    const resetTime = stored.resetTime; // Already in seconds from backend
    
    // Validate the stored data to prevent corruption
    if (resetTime <= 0 || resetTime < now - 3600 || resetTime > now + 3600) {
      console.warn('🧹 Invalid resetTime detected, clearing rate limit data');
      this.clearRateLimit();
      return;
    }
    
    // Calculate remaining time directly from reset time
    const remainingTime = Math.max(0, resetTime - now);

    if (remainingTime > 0 && remainingTime <= 3600) { // Max 1 hour to prevent corruption
      // Directly update status and start countdown with remaining time
      this.updateRateLimitStatus({
        isRateLimited: true,
        retryAfter: stored.retryAfter,
        remainingTime,
        message: this.generateRateLimitMessage(remainingTime, stored.limit),
        limit: stored.limit,
        remaining: 0,
        resetTime: stored.resetTime
      });
      
      // Start countdown from remaining time
      this.startCountdownFromRemainingTime(remainingTime, stored);
    } else {
      // Rate limit has expired or data is invalid
      this.clearRateLimit();
    }
  }

  /**
   * Start countdown from a specific remaining time (for restore from storage)
   */
  private startCountdownFromRemainingTime(remainingTime: number, originalStatus: RateLimitStatus): void {
    // Stop any existing countdown
    if (this.countdownSubscription) {
      this.countdownSubscription.unsubscribe();
    }

    let currentRemainingTime = remainingTime;
    
    // Start countdown
    this.countdownSubscription = timer(0, 1000).pipe(
      takeWhile(() => currentRemainingTime > 0),
      tap(() => {
        currentRemainingTime--;
        this.updateRateLimitStatus({
          isRateLimited: currentRemainingTime > 0,
          retryAfter: originalStatus.retryAfter,
          remainingTime: Math.max(0, currentRemainingTime),
          message: currentRemainingTime > 0 
            ? this.generateRateLimitMessage(currentRemainingTime, originalStatus.limit)
            : 'Rate limit has been reset. You can try again now.',
          limit: originalStatus.limit,
          remaining: currentRemainingTime <= 0 ? originalStatus.limit : 0,
          resetTime: originalStatus.resetTime
        });
      }),
      finalize(() => {
        // Clear when countdown completes
        this.clearRateLimit();
      })
    ).subscribe();
  }

  /**
   * Get stored rate limit status from localStorage
   */
  private getStoredStatus(): RateLimitStatus | null {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : null;
    } catch {
      return null;
    }
  }

  /**
   * Save rate limit status to localStorage
   */
  private saveToStorage(status: RateLimitStatus): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(status));
    } catch {
      // Ignore localStorage errors
    }
  }

  /**
   * Clear rate limit from localStorage
   */
  private clearStorage(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch {
      // Ignore localStorage errors
    }
  }  /**
   * Handle rate limit response from server
   * Enhanced to check for actual rate limit indicators in error messages
   */
  handleRateLimitResponse(headers: any, error?: any): void {
    const retryAfter = parseInt(headers['retry-after'] || headers['x-ratelimit-retryafter'] || '0');
    const limit = parseInt(headers['x-ratelimit-limit'] || '0');
    const remaining = parseInt(headers['x-ratelimit-remaining'] || '0');
    const resetTime = parseInt(headers['x-ratelimit-reset'] || '0'); // Already in seconds from backend
    const totalRequests = parseInt(headers['x-ratelimit-totalrequests'] || '0');

    // Check if this is actually a rate limit by examining error message
    if (error && !this.isActualRateLimit(error)) {
      console.log('❌ Not processing as rate limit - error does not contain rate limit indicators');
      return;
    }

    if (retryAfter > 0) {
      console.log('✅ Processing rate limit with retry-after:', retryAfter);
      this.startRateLimitCountdown(retryAfter, limit, remaining, resetTime, totalRequests);
    } else {
      this.updateRateLimitStatus({
        isRateLimited: false,
        retryAfter: 0,
        remainingTime: 0,
        message: '',
        limit,
        remaining,
        resetTime
      });
    }
  }

  /**
   * Check if an error actually indicates a rate limit
   */
  private isActualRateLimit(error: any): boolean {
    if (!error) return true; // If no error object, assume it's from headers
    
    const errorMessage = error.message?.toLowerCase() || '';
    const errorText = error.error?.toString?.()?.toLowerCase() || '';
    const errorDetails = error.error?.error?.message?.toLowerCase() || '';
    const statusText = error.statusText?.toLowerCase() || '';
    
    // Must contain "rate" or very specific rate limit terms
    const rateLimitKeywords = [
      'rate limit',
      'ratelimit',
      'rate-limit',
      'too many requests',
      'throttled',
      'throttle',
      'quota exceeded'
    ];
    
    const hasRateLimitKeyword = rateLimitKeywords.some(keyword => 
      errorMessage.includes(keyword) || 
      errorText.includes(keyword) || 
      errorDetails.includes(keyword) ||
      statusText.includes(keyword)
    );
    
    console.log('🔍 Checking if error is actual rate limit:', {
      hasKeyword: hasRateLimitKeyword,
      message: errorMessage.substring(0, 100),
      status: error.status
    });
    
    return hasRateLimitKeyword || error.status === 429;
  }
  /**
   * Start countdown timer for rate limit
   */
  private startRateLimitCountdown(
    retryAfter: number, 
    limit: number, 
    remaining: number, 
    resetTime: number,
    totalRequests: number
  ): void {
    // Stop any existing countdown
    if (this.countdownSubscription) {
      this.countdownSubscription.unsubscribe();
    }

    let remainingTime = retryAfter;
    
    // Initial status
    this.updateRateLimitStatus({
      isRateLimited: true,
      retryAfter,
      remainingTime,
      message: this.generateRateLimitMessage(remainingTime, totalRequests),
      limit,
      remaining: 0,
      resetTime
    });

    // Start countdown
    this.countdownSubscription = timer(0, 1000).pipe(
      takeWhile(() => remainingTime > 0),
      tap(() => {
        remainingTime--;
        this.updateRateLimitStatus({
          isRateLimited: remainingTime > 0,
          retryAfter,
          remainingTime: Math.max(0, remainingTime),
          message: remainingTime > 0 
            ? this.generateRateLimitMessage(remainingTime, totalRequests)
            : 'Rate limit has been reset. You can try again now.',
          limit,
          remaining: remainingTime <= 0 ? limit : 0,
          resetTime
        });
      }),
      finalize(() => {
        // Rate limit period has ended
        this.clearRateLimit();
      })
    ).subscribe();
  }

  /**
   * Generate user-friendly rate limit message
   */
  private generateRateLimitMessage(remainingTime: number, totalRequests: number): string {
    const minutes = Math.floor(remainingTime / 60);
    const seconds = remainingTime % 60;
    
    let timeString = '';
    if (minutes > 0) {
      timeString = `${minutes} minute${minutes > 1 ? 's' : ''} and ${seconds} second${seconds !== 1 ? 's' : ''}`;
    } else {
      timeString = `${seconds} second${seconds !== 1 ? 's' : ''}`;
    }

    return `🚫 Rate limit exceeded! You've made too many requests. Please wait ${timeString} before trying again. (Total requests: ${totalRequests})`;
  }
  /**
   * Update rate limit status
   */
  private updateRateLimitStatus(status: RateLimitStatus): void {
    this.rateLimitSubject.next(status);
    // Save to localStorage for persistence across page reloads
    this.saveToStorage(status);
  }
  /**
   * Clear rate limit status
   */
  clearRateLimit(): void {
    if (this.countdownSubscription) {
      this.countdownSubscription.unsubscribe();
      this.countdownSubscription = undefined;
    }

    this.updateRateLimitStatus({
      isRateLimited: false,
      retryAfter: 0,
      remainingTime: 0,
      message: '',
      limit: 0,
      remaining: 0,
      resetTime: 0
    });
    
    // Clear from localStorage
    this.clearStorage();
  }

  /**
   * Get current rate limit status (synchronous)
   */
  getCurrentStatus(): RateLimitStatus {
    return this.rateLimitSubject.value;
  }

  /**
   * Check if currently rate limited
   */
  isRateLimited(): boolean {
    return this.rateLimitSubject.value.isRateLimited;
  }

  /**
   * Get remaining requests
   */
  getRemainingRequests(): number {
    return this.rateLimitSubject.value.remaining;
  }

  /**
   * Format time remaining for display
   */
  static formatTimeRemaining(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes === 0) {
      return `${remainingSeconds}s`;
    }
    
    return `${minutes}m ${remainingSeconds}s`;
  }

  /**
   * Debug method to trigger rate limit popup (for testing)
   */
  debugTriggerRateLimit(seconds: number = 60): void {
    console.log('🧪 Debug: Triggering rate limit popup for', seconds, 'seconds');
    this.startRateLimitCountdown(seconds, 2, 0, Math.floor(Date.now() / 1000) + seconds, 2);
  }
}
