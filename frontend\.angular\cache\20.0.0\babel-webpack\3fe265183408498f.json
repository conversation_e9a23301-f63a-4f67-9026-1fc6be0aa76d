{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { Disable2FADialogComponent } from '../disable-2fa-dialog/disable-2fa-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/two-factor.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/material/snack-bar\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"../../../services/auth.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/progress-spinner\";\nimport * as i13 from \"@angular/material/divider\";\nimport * as i14 from \"./two-factor-setup.component\";\nfunction TwoFactorManagementComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"mat-spinner\", 5);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TwoFactorManagementComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-two-factor-setup\", 6);\n    i0.ɵɵlistener(\"setupComplete\", function TwoFactorManagementComponent_div_2_Template_app_two_factor_setup_setupComplete_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSetupComplete());\n    })(\"cancelled\", function TwoFactorManagementComponent_div_2_Template_app_two_factor_setup_cancelled_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSetupCancelled());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TwoFactorManagementComponent_div_3_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"mat-icon\", 13);\n    i0.ɵɵtext(3, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 14)(5, \"p\")(6, \"strong\");\n    i0.ɵɵtext(7, \"Enhance your account security\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"Two-factor authentication adds an extra layer of security to your account by requiring a second form of verification when signing in.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 15)(11, \"h4\");\n    i0.ɵɵtext(12, \"Benefits:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"ul\")(14, \"li\");\n    i0.ɵɵtext(15, \"Protects against password theft\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"li\");\n    i0.ɵɵtext(17, \"Reduces risk of unauthorized access\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"li\");\n    i0.ɵɵtext(19, \"Works with popular authenticator apps\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"li\");\n    i0.ɵɵtext(21, \"Includes backup codes for recovery\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(22, \"div\", 16)(23, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function TwoFactorManagementComponent_div_3_div_12_Template_button_click_23_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.enableTwoFactor());\n    });\n    i0.ɵɵelementStart(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"add_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26, \" Enable Two-Factor Authentication \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(23);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n  }\n}\nfunction TwoFactorManagementComponent_div_3_div_13_div_41_mat_error_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Password is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TwoFactorManagementComponent_div_3_div_13_div_41_mat_error_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" 2FA code is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TwoFactorManagementComponent_div_3_div_13_div_41_mat_error_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Code must be 6 digits \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TwoFactorManagementComponent_div_3_div_13_div_41_mat_spinner_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 44);\n  }\n}\nfunction TwoFactorManagementComponent_div_3_div_13_div_41_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Disable 2FA\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TwoFactorManagementComponent_div_3_div_13_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"mat-card\", 35)(2, \"mat-card-header\")(3, \"mat-card-title\");\n    i0.ɵɵtext(4, \"Disable Two-Factor Authentication\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-card-subtitle\");\n    i0.ɵɵtext(6, \"Enter your password and a 2FA code to confirm\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"mat-card-content\")(8, \"form\", 36);\n    i0.ɵɵlistener(\"ngSubmit\", function TwoFactorManagementComponent_div_3_div_13_div_41_Template_form_ngSubmit_8_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onDisableTwoFactor());\n    });\n    i0.ɵɵelementStart(9, \"mat-form-field\", 37)(10, \"mat-label\");\n    i0.ɵɵtext(11, \"Current Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 38);\n    i0.ɵɵtemplate(13, TwoFactorManagementComponent_div_3_div_13_div_41_mat_error_13_Template, 2, 0, \"mat-error\", 2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-form-field\", 37)(15, \"mat-label\");\n    i0.ɵɵtext(16, \"6-digit 2FA Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 39);\n    i0.ɵɵtemplate(18, TwoFactorManagementComponent_div_3_div_13_div_41_mat_error_18_Template, 2, 0, \"mat-error\", 2)(19, TwoFactorManagementComponent_div_3_div_13_div_41_mat_error_19_Template, 2, 0, \"mat-error\", 2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 40)(21, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function TwoFactorManagementComponent_div_3_div_13_div_41_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.hideDisableForm());\n    });\n    i0.ɵɵtext(22, \" Cancel \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 42);\n    i0.ɵɵtemplate(24, TwoFactorManagementComponent_div_3_div_13_div_41_mat_spinner_24_Template, 1, 0, \"mat-spinner\", 43)(25, TwoFactorManagementComponent_div_3_div_13_div_41_span_25_Template, 2, 0, \"span\", 2);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.disableForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r1.disableForm.get(\"password\")) == null ? null : tmp_4_0.hasError(\"required\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx_r1.disableForm.get(\"code\")) == null ? null : tmp_5_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx_r1.disableForm.get(\"code\")) == null ? null : tmp_6_0.hasError(\"pattern\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading || ctx_r1.disableForm.invalid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loading);\n  }\n}\nfunction TwoFactorManagementComponent_div_3_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"mat-icon\", 20);\n    i0.ɵɵtext(3, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 21)(5, \"p\")(6, \"strong\");\n    i0.ɵɵtext(7, \"Your account is protected\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"Two-factor authentication is active on your account. You'll be prompted for a verification code when signing in from new devices.\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 22)(11, \"div\", 23)(12, \"h4\");\n    i0.ɵɵtext(13, \"Test & Manage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 24)(15, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function TwoFactorManagementComponent_div_3_div_13_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.sendTestCode());\n    });\n    i0.ɵɵelementStart(16, \"mat-icon\");\n    i0.ɵɵtext(17, \"send\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18, \" Send Test Code \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(19, \"mat-divider\");\n    i0.ɵɵelementStart(20, \"div\", 26)(21, \"h4\");\n    i0.ɵɵtext(22, \"Danger Zone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"p\", 27)(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26, \" Disabling 2FA will make your account less secure \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 28)(28, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function TwoFactorManagementComponent_div_3_div_13_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showDisableForm2FA());\n    });\n    i0.ɵɵelementStart(29, \"mat-icon\");\n    i0.ɵɵtext(30, \"remove_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31, \" Disable with Password & Code \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 30)(33, \"p\", 31)(34, \"mat-icon\");\n    i0.ɵɵtext(35, \"help_outline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(36, \" Can't access your authenticator device? \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function TwoFactorManagementComponent_div_3_div_13_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openEmailDisableDialog());\n    });\n    i0.ɵɵelementStart(38, \"mat-icon\");\n    i0.ɵɵtext(39, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(40, \" Disable via Email Verification \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵtemplate(41, TwoFactorManagementComponent_div_3_div_13_div_41_Template, 26, 8, \"div\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading || ctx_r1.showDisableForm);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.loading);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showDisableForm);\n  }\n}\nfunction TwoFactorManagementComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"mat-card\", 8)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Two-Factor Authentication \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n    i0.ɵɵtext(8, \" Status: \");\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"mat-card-content\");\n    i0.ɵɵtemplate(12, TwoFactorManagementComponent_div_3_div_12_Template, 27, 1, \"div\", 9)(13, TwoFactorManagementComponent_div_3_div_13_Template, 42, 4, \"div\", 10);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r1.twoFactorStatus.enabled ? \"enabled-icon\" : \"disabled-icon\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.twoFactorStatus.enabled ? \"security\" : \"security\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r1.twoFactorStatus.enabled ? \"status-enabled\" : \"status-disabled\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.twoFactorStatus.enabled ? \"Enabled\" : \"Disabled\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.twoFactorStatus.enabled);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.twoFactorStatus.enabled);\n  }\n}\nexport let TwoFactorManagementComponent = /*#__PURE__*/(() => {\n  class TwoFactorManagementComponent {\n    constructor(twoFactorService, formBuilder, snackBar, dialog, authService) {\n      this.twoFactorService = twoFactorService;\n      this.formBuilder = formBuilder;\n      this.snackBar = snackBar;\n      this.dialog = dialog;\n      this.authService = authService;\n      this.twoFactorStatus = {\n        enabled: false\n      };\n      this.loading = false;\n      this.showSetup = false;\n      this.showDisableForm = false;\n      this.disableForm = this.formBuilder.group({\n        password: ['', [Validators.required]],\n        code: ['', [Validators.required, Validators.pattern(/^\\d{6}$/)]]\n      });\n    }\n    ngOnInit() {\n      this.loadStatus();\n    }\n    loadStatus() {\n      this.loading = true;\n      this.twoFactorService.get2FAStatus().subscribe({\n        next: status => {\n          this.twoFactorStatus = status;\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Failed to load 2FA status:', error);\n          this.loading = false;\n        }\n      });\n    }\n    enableTwoFactor() {\n      this.showSetup = true;\n    }\n    onSetupComplete() {\n      this.showSetup = false;\n      this.loadStatus();\n      this.snackBar.open('Two-factor authentication has been enabled!', 'Close', {\n        duration: 5000\n      });\n    }\n    onSetupCancelled() {\n      this.showSetup = false;\n    }\n    showDisableForm2FA() {\n      this.showDisableForm = true;\n    }\n    hideDisableForm() {\n      this.showDisableForm = false;\n      this.disableForm.reset();\n    }\n    onDisableTwoFactor() {\n      if (this.disableForm.invalid) {\n        this.markFormGroupTouched(this.disableForm);\n        return;\n      }\n      this.loading = true;\n      const {\n        password,\n        code\n      } = this.disableForm.value;\n      this.twoFactorService.disable2FA(code).subscribe({\n        next: response => {\n          this.snackBar.open('Two-factor authentication has been disabled!', 'Close', {\n            duration: 5000\n          });\n          this.hideDisableForm();\n          this.loadStatus();\n          this.loading = false;\n        },\n        error: error => {\n          this.snackBar.open(error.error?.message || 'Failed to disable 2FA', 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n        }\n      });\n    }\n    sendTestCode() {\n      this.loading = true;\n      this.twoFactorService.send2FASMS().subscribe({\n        next: response => {\n          this.snackBar.open('Test code sent to your phone!', 'Close', {\n            duration: 3000\n          });\n          this.loading = false;\n        },\n        error: error => {\n          this.snackBar.open(error.error?.message || 'Failed to send test code', 'Close', {\n            duration: 5000\n          });\n          this.loading = false;\n        }\n      });\n    }\n    /**\n     * Open email disable dialog for users who can't access their authenticator\n     */\n    openEmailDisableDialog() {\n      const currentUser = this.authService.currentUserValue;\n      if (!currentUser) {\n        this.snackBar.open('User information not available. Please refresh and try again.', 'Close', {\n          duration: 5000\n        });\n        return;\n      }\n      const dialogRef = this.dialog.open(Disable2FADialogComponent, {\n        width: '500px',\n        maxWidth: '90vw',\n        disableClose: false,\n        data: {\n          email: currentUser.email,\n          allCodesUsed: false,\n          // From profile, codes may not be exhausted\n          source: 'profile'\n        }\n      });\n      dialogRef.afterClosed().subscribe(result => {\n        if (result?.success) {\n          // Email sent successfully, show confirmation\n          this.snackBar.open('Disable confirmation email sent! Check your inbox for the confirmation link.', 'Close', {\n            duration: 8000,\n            panelClass: ['success-snackbar']\n          });\n        }\n      });\n    }\n    markFormGroupTouched(formGroup) {\n      Object.keys(formGroup.controls).forEach(key => {\n        const control = formGroup.get(key);\n        control?.markAsTouched();\n      });\n    }\n    static #_ = this.ɵfac = function TwoFactorManagementComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TwoFactorManagementComponent)(i0.ɵɵdirectiveInject(i1.TwoFactorService), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.MatSnackBar), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i5.AuthService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TwoFactorManagementComponent,\n      selectors: [[\"app-two-factor-management\"]],\n      standalone: false,\n      decls: 4,\n      vars: 3,\n      consts: [[1, \"two-factor-management\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"management-container\", 4, \"ngIf\"], [1, \"loading-container\"], [\"diameter\", \"30\"], [3, \"setupComplete\", \"cancelled\"], [1, \"management-container\"], [1, \"status-card\"], [\"class\", \"disabled-state\", 4, \"ngIf\"], [\"class\", \"enabled-state\", 4, \"ngIf\"], [1, \"disabled-state\"], [1, \"info-section\"], [1, \"info-icon\"], [1, \"info-content\"], [1, \"benefits\"], [1, \"action-section\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [1, \"enabled-state\"], [1, \"success-section\"], [1, \"success-icon\"], [1, \"success-content\"], [1, \"management-actions\"], [1, \"action-group\"], [1, \"button-group\"], [\"mat-stroked-button\", \"\", 3, \"click\", \"disabled\"], [1, \"danger-zone\"], [1, \"warning-text\"], [1, \"disable-options\"], [\"mat-stroked-button\", \"\", \"color\", \"warn\", 3, \"click\", \"disabled\"], [1, \"alternative-disable\"], [1, \"help-text\"], [\"mat-button\", \"\", \"color\", \"accent\", 3, \"click\", \"disabled\"], [\"class\", \"disable-form-container\", 4, \"ngIf\"], [1, \"disable-form-container\"], [1, \"disable-card\"], [3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"password\", \"formControlName\", \"password\", \"autocomplete\", \"current-password\"], [\"matInput\", \"\", \"formControlName\", \"code\", \"placeholder\", \"123456\", \"maxlength\", \"6\", \"autocomplete\", \"off\"], [1, \"form-actions\"], [\"mat-button\", \"\", \"type\", \"button\", 3, \"click\", \"disabled\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", \"type\", \"submit\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"diameter\", \"20\"]],\n      template: function TwoFactorManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, TwoFactorManagementComponent_div_1_Template, 2, 0, \"div\", 1)(2, TwoFactorManagementComponent_div_2_Template, 2, 0, \"div\", 2)(3, TwoFactorManagementComponent_div_3_Template, 14, 8, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.loading && !ctx.showSetup);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showSetup);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.showSetup);\n        }\n      },\n      dependencies: [i6.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.MaxLengthValidator, i2.FormGroupDirective, i2.FormControlName, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardSubtitle, i7.MatCardTitle, i8.MatButton, i9.MatIcon, i10.MatFormField, i10.MatLabel, i10.MatError, i11.MatInput, i12.MatProgressSpinner, i13.MatDivider, i14.TwoFactorSetupComponent],\n      styles: [\".two-factor-management[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]{text-align:center;padding:40px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .enabled-icon[_ngcontent-%COMP%]{color:#4caf50}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]   .disabled-icon[_ngcontent-%COMP%]{color:#757575}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%]   .status-enabled[_ngcontent-%COMP%]{color:#4caf50;font-weight:500}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-subtitle[_ngcontent-%COMP%]   .status-disabled[_ngcontent-%COMP%]{color:#757575;font-weight:500}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:24px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-icon[_ngcontent-%COMP%]{color:#2196f3;font-size:28px;width:28px;height:28px;margin-top:4px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%]{flex:1}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 12px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-of-type{margin-bottom:16px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%]   .benefits[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 8px;color:#333}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%]   .benefits[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:0;padding-left:20px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-content[_ngcontent-%COMP%]   .benefits[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:4px;color:#666}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]{text-align:center;padding-top:16px;border-top:1px solid #e0e0e0}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .action-section[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:8px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:24px;padding:16px;background-color:#e8f5e8;border-radius:8px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%]{color:#4caf50;font-size:28px;width:28px;height:28px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]{flex:1}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 8px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]   .success-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child{margin-bottom:0}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]{margin-bottom:24px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 12px;color:#333}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:8px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   mat-divider[_ngcontent-%COMP%]{margin:24px 0}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 12px;color:#d32f2f}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .warning-text[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin:0 0 16px;color:#f57c00;font-size:14px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .warning-text[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%] > button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:8px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]   .alternative-disable[_ngcontent-%COMP%]{background:#f8f9fa;border:1px solid #e9ecef;border-radius:8px;padding:16px;text-align:center}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]   .alternative-disable[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:8px;margin:0 0 12px;color:#6c757d;font-size:14px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]   .alternative-disable[_ngcontent-%COMP%]   .help-text[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#6c757d}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]   .alternative-disable[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:8px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]{margin-top:24px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]{border:1px solid #ffcdd2}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   mat-card-header[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%]{color:#d32f2f}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%]{width:100%;margin-bottom:16px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-top:16px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:120px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%]{margin-right:8px}@media (max-width: 600px){.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]{flex-direction:column;text-align:center}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .disabled-state[_ngcontent-%COMP%]   .info-section[_ngcontent-%COMP%]   .info-icon[_ngcontent-%COMP%]{align-self:center}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]{flex-direction:column;text-align:center}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .success-section[_ngcontent-%COMP%]   .success-icon[_ngcontent-%COMP%]{align-self:center}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .action-group[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%] > button[_ngcontent-%COMP%]{width:100%}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .management-actions[_ngcontent-%COMP%]   .danger-zone[_ngcontent-%COMP%]   .disable-options[_ngcontent-%COMP%]   .alternative-disable[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]{flex-direction:column;gap:12px}.two-factor-management[_ngcontent-%COMP%]   .management-container[_ngcontent-%COMP%]   .status-card[_ngcontent-%COMP%]   .enabled-state[_ngcontent-%COMP%]   .disable-form-container[_ngcontent-%COMP%]   .disable-card[_ngcontent-%COMP%]   .form-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%}}\"]\n    });\n  }\n  return TwoFactorManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}