"""User Model."""
from masoniteorm.models import Model
from masoniteorm.scopes import SoftDeletesMixin
from masonite.authentication import Authenticates
from masonite.api.authentication import AuthenticatesTokens
import bcrypt
import secrets
import jwt
from datetime import datetime, timedelta, timezone
from masonite.environment import env


class User(Model, SoftDeletesMixin, Authenticates, AuthenticatesTokens):
    """User Model with JWT Authentication Support."""

    __fillable__ = [
        "name", "email", "password", "email_verified_at", "two_factor_enabled",
        "first_name", "last_name", "phone", "email_verification_token",
        "email_verification_expires", "password_reset_token", "password_reset_expires",
        "two_factor_secret", "last_login_at", "is_active", "roles", "api_token",
        "avatar_url", "oauth_providers",  # OAuth fields
        # Security fields
        "login_attempts", "locked_until", "last_failed_login", "last_login_ip",
        "last_login_user_agent", "force_password_change", "password_changed_at"
    ]
    __hidden__ = ["password", "api_token", "two_factor_secret", "email_verification_token", 
                  "password_reset_token"]
    __auth__ = "email"
    
    # JWT token column (configurable)
    __TOKEN_COLUMN__ = "api_token"
    
    def set_password(self, password):
        """Hash password using bcrypt"""
        self.password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        return self
    
    def verify_password(self, password):
        """Verify password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), self.password.encode('utf-8'))
    
    def generate_api_token(self):
        """Generate a new JWT API token compatible with LoopBack frontend"""
        # JWT payload matching LoopBack format
        payload = {
            'securityId': str(self.id),  # LoopBack uses securityId
            'id': str(self.id),
            'email': self.email,
            'roles': [self.roles] if self.roles else ['user'],
            'iat': datetime.now().timestamp(),
            'exp': datetime.now().timestamp() + (env('JWT_EXPIRES_IN', 1440) * 60),  # 24 hours default
            'iss': env('JWT_ISSUER', 'masonite-backend'),
            'aud': env('JWT_AUDIENCE', 'masonite-frontend')
        }

        # Generate JWT token
        token = jwt.encode(
            payload,
            env('JWT_SECRET', env('APP_KEY')),
            algorithm=env('JWT_ALGORITHM', 'HS256')
        )

        # Store token in database for validation (optional)
        self.api_token = token
        self.save()
        return token
    
    def is_email_verified(self):
        """Check if email is verified"""
        return self.email_verified_at is not None
    
    def mark_email_as_verified(self):
        """Mark email as verified"""
        self.email_verified_at = datetime.now()
        self.email_verification_token = None
        self.email_verification_expires = None
        self.save()
        return self
    
    def generate_email_verification_token(self):
        """Generate email verification token"""
        token = secrets.token_urlsafe(32)
        # Token expires in 24 hours (matching LoopBack)
        expires = datetime.now() + timedelta(hours=24)
        
        self.email_verification_token = token
        self.email_verification_expires = expires
        self.save()
        return token
    
    def generate_password_reset_token(self):
        """Generate password reset token"""
        token = secrets.token_urlsafe(32)
        # Token expires in 1 hour (matching LoopBack)
        expires = datetime.now() + timedelta(hours=1)
        
        self.password_reset_token = token
        self.password_reset_expires = expires
        self.save()
        return token
    
    def clear_password_reset_token(self):
        """Clear password reset token"""
        self.password_reset_token = None
        self.password_reset_expires = None
        self.save()
        return self

    def serialize_user(self):
        """Serialize user data for API responses (exclude sensitive fields)."""
        return {
            'id': self.id,
            'email': self.email,
            'name': self.name,
            'firstName': self.first_name,
            'lastName': self.last_name,
            'phone': self.phone,
            'emailVerified': self.email_verified_at is not None,
            'twoFactorEnabled': self.two_factor_enabled or False,
            'isActive': self.is_active if hasattr(self, 'is_active') else True,
            'roles': self.roles or 'user',
            'lastLoginAt': self.last_login_at.isoformat() if hasattr(self, 'last_login_at') and self.last_login_at else None,
            'avatarUrl': self.avatar_url,  # OAuth avatar
            'oauthProviders': self.oauth_providers or [],  # Connected OAuth providers
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None
        }

    # Security Methods
    def increment_login_attempts(self):
        """Increment failed login attempts"""
        if not hasattr(self, 'login_attempts') or self.login_attempts is None:
            self.login_attempts = 0
        self.login_attempts += 1
        self.last_failed_login = datetime.now()
        self.save()

    def reset_login_attempts(self):
        """Reset login attempts on successful login"""
        self.login_attempts = 0
        self.locked_until = None
        self.last_failed_login = None
        self.last_login_at = datetime.now()
        self.save()

    def is_locked(self):
        """Check if account is locked"""
        if not hasattr(self, 'locked_until') or not self.locked_until:
            return False

        if isinstance(self.locked_until, str):
            locked_until = datetime.fromisoformat(self.locked_until.replace('Z', '+00:00'))
        else:
            locked_until = self.locked_until

        return datetime.now() < locked_until

    def lock_account(self, duration_minutes=30):
        """Lock account for specified duration"""
        self.locked_until = datetime.now() + timedelta(minutes=duration_minutes)
        self.save()

    def unlock_account(self):
        """Unlock account"""
        self.locked_until = None
        self.login_attempts = 0
        self.save()

    def should_be_locked(self, max_attempts=5):
        """Check if account should be locked based on failed attempts"""
        if not hasattr(self, 'login_attempts'):
            return False
        return self.login_attempts >= max_attempts

    def update_login_info(self, ip_address=None, user_agent=None):
        """Update login information"""
        self.last_login_at = datetime.now()
        self.last_login_ip = ip_address
        self.last_login_user_agent = user_agent
        self.login_attempts = 0
        self.locked_until = None
        self.save()

    def force_password_change_on_next_login(self):
        """Force user to change password on next login"""
        self.force_password_change = True
        self.save()

    def mark_password_changed(self):
        """Mark password as changed"""
        self.password_changed_at = datetime.now()
        self.force_password_change = False
        self.save()
