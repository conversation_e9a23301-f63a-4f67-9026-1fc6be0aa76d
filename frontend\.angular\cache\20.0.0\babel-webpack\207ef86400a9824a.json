{"ast": null, "code": "import { Subscriber } from '../Subscriber';\nexport function createOperatorSubscriber(destination, onNext, onComplete, onError, onFinalize) {\n  return new OperatorSubscriber(destination, onNext, onComplete, onError, onFinalize);\n}\nexport class OperatorSubscriber extends Subscriber {\n  constructor(destination, onNext, onComplete, onError, onFinalize, shouldUnsubscribe) {\n    super(destination);\n    this.onFinalize = onFinalize;\n    this.shouldUnsubscribe = shouldUnsubscribe;\n    this._next = onNext ? function (value) {\n      try {\n        onNext(value);\n      } catch (err) {\n        destination.error(err);\n      }\n    } : super._next;\n    this._error = onError ? function (err) {\n      try {\n        onError(err);\n      } catch (err) {\n        destination.error(err);\n      } finally {\n        this.unsubscribe();\n      }\n    } : super._error;\n    this._complete = onComplete ? function () {\n      try {\n        onComplete();\n      } catch (err) {\n        destination.error(err);\n      } finally {\n        this.unsubscribe();\n      }\n    } : super._complete;\n  }\n  unsubscribe() {\n    var _a;\n    if (!this.shouldUnsubscribe || this.shouldUnsubscribe()) {\n      const {\n        closed\n      } = this;\n      super.unsubscribe();\n      !closed && ((_a = this.onFinalize) === null || _a === void 0 ? void 0 : _a.call(this));\n    }\n  }\n}\n//# sourceMappingURL=OperatorSubscriber.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}