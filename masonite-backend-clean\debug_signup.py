#!/usr/bin/env python3
"""
Debug signup issues
"""

import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:3002/api"
HEADERS = {
    'Content-Type': 'application/json',
    'Origin': 'http://localhost:4200'
}

def test_simple_signup():
    """Test simple signup"""
    print("🧪 Testing Simple Signup...")
    
    signup_data = {
        "firstName": "Debug",
        "lastName": "Test",
        "email": f"debug_{int(time.time())}@example.com",
        "password": "DebugTest123!",
        "confirmPassword": "DebugTest123!"
    }
    
    try:
        print(f"📤 Sending: {json.dumps(signup_data, indent=2)}")
        response = requests.post(f"{BASE_URL}/auth/signup", json=signup_data, headers=HEADERS, timeout=10)
        print(f"📊 Status: {response.status_code}")
        print(f"📊 Headers: {dict(response.headers)}")
        print(f"📊 Response: {response.text}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def test_server_health():
    """Test server health"""
    print("🧪 Testing Server Health...")
    
    try:
        response = requests.get("http://localhost:3002", timeout=5)
        print(f"✅ Server health: {response.status_code}")
    except Exception as e:
        print(f"❌ Server health error: {e}")

def main():
    """Main debug function"""
    print("🔍 Debug Signup Issues")
    print("=" * 40)
    
    test_server_health()
    test_simple_signup()

if __name__ == "__main__":
    main()
