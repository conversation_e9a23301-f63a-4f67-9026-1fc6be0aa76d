{"ast": null, "code": "import { switchMap } from './switchMap';\nimport { isFunction } from '../util/isFunction';\nexport function switchMapTo(innerObservable, resultSelector) {\n  return isFunction(resultSelector) ? switchMap(() => innerObservable, resultSelector) : switchMap(() => innerObservable);\n}\n//# sourceMappingURL=switchMapTo.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}