{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../services/two-factor.service\";\nimport * as i3 from \"../../../services/auth.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/progress-spinner\";\nfunction Disable2FAConfirmComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"mat-spinner\", 6);\n    i0.ɵɵelementStart(2, \"h2\");\n    i0.ɵɵtext(3, \"Processing your request...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Please wait while we disable two-factor authentication on your account.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction Disable2FAConfirmComponent_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"h3\");\n    i0.ɵɵtext(2, \"Account Details:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\")(4, \"strong\");\n    i0.ɵɵtext(5, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\")(8, \"strong\");\n    i0.ɵɵtext(9, \"Name:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.userInfo.email);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.userInfo.firstName, \" \", ctx_r1.userInfo.lastName);\n  }\n}\nfunction Disable2FAConfirmComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"mat-icon\", 8);\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h2\");\n    i0.ɵɵtext(4, \"Two-Factor Authentication Disabled\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 9);\n    i0.ɵɵtext(6, \" Two-factor authentication has been successfully disabled on your account. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, Disable2FAConfirmComponent_div_3_div_7_Template, 11, 3, \"div\", 10);\n    i0.ɵɵelementStart(8, \"div\", 11)(9, \"mat-icon\", 12);\n    i0.ɵɵtext(10, \"warning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 13)(12, \"h4\");\n    i0.ɵɵtext(13, \"Important Security Notice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\");\n    i0.ɵɵtext(15, \"Your account is now less secure without two-factor authentication. Consider:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"ul\")(17, \"li\");\n    i0.ɵɵtext(18, \"Using a strong, unique password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"li\");\n    i0.ɵɵtext(20, \"Re-enabling 2FA from your profile settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"li\");\n    i0.ɵɵtext(22, \"Monitoring your account for any suspicious activity\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"div\", 14)(24, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function Disable2FAConfirmComponent_div_3_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToDashboard());\n    });\n    i0.ɵɵelementStart(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"dashboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(27, \" Go to Dashboard \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function Disable2FAConfirmComponent_div_3_Template_button_click_28_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToProfile());\n    });\n    i0.ɵɵelementStart(29, \"mat-icon\");\n    i0.ɵɵtext(30, \"settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31, \" Profile Settings \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userInfo);\n  }\n}\nfunction Disable2FAConfirmComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"mat-icon\", 19);\n    i0.ɵɵtext(2, \"error\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h2\");\n    i0.ɵɵtext(4, \"Unable to Disable 2FA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 20);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 21)(8, \"h4\");\n    i0.ɵɵtext(9, \"What you can do:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"ul\")(11, \"li\");\n    i0.ɵɵtext(12, \"Check if the link has expired (links are valid for 1 hour)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"li\");\n    i0.ɵɵtext(14, \"Request a new disable email from the login screen\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"li\");\n    i0.ɵɵtext(16, \"Contact support if you continue to have issues\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 14)(18, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function Disable2FAConfirmComponent_div_4_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goToLogin());\n    });\n    i0.ɵɵelementStart(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Back to Login \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function Disable2FAConfirmComponent_div_4_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.requestNewLink());\n    });\n    i0.ɵɵelementStart(23, \"mat-icon\");\n    i0.ɵɵtext(24, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25, \" Request New Link \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.errorMessage);\n  }\n}\nexport let Disable2FAConfirmComponent = /*#__PURE__*/(() => {\n  class Disable2FAConfirmComponent {\n    constructor(route, router, twoFactorService, authService, snackBar) {\n      this.route = route;\n      this.router = router;\n      this.twoFactorService = twoFactorService;\n      this.authService = authService;\n      this.snackBar = snackBar;\n      this.loading = true;\n      this.success = false;\n      this.errorMessage = '';\n      this.userInfo = null;\n    }\n    ngOnInit() {\n      const token = this.route.snapshot.queryParamMap.get('token');\n      if (!token) {\n        this.loading = false;\n        this.success = false;\n        this.errorMessage = 'Invalid or missing disable token. Please request a new disable link.';\n        return;\n      }\n      this.confirmDisable2FA(token);\n    }\n    confirmDisable2FA(token) {\n      this.twoFactorService.confirmDisable2FA(token).subscribe({\n        next: response => {\n          this.loading = false;\n          this.success = response.success;\n          this.userInfo = response.user;\n          if (response.success) {\n            // Update current user if they're logged in\n            if (this.authService.currentUserValue) {\n              const updatedUser = {\n                ...this.authService.currentUserValue\n              };\n              updatedUser.twoFactorEnabled = false;\n              // Update the user in auth service if needed\n            }\n            this.snackBar.open('Two-factor authentication has been disabled successfully.', 'Close', {\n              duration: 5000,\n              panelClass: ['success-snackbar']\n            });\n          } else {\n            this.errorMessage = response.message || 'Failed to disable 2FA';\n          }\n        },\n        error: error => {\n          console.error('Error confirming 2FA disable:', error);\n          this.loading = false;\n          this.success = false;\n          if (error.status === 400) {\n            this.errorMessage = 'The disable link has expired or is invalid. Please request a new one.';\n          } else {\n            this.errorMessage = error.error?.message || 'An unexpected error occurred. Please try again.';\n          }\n        }\n      });\n    }\n    goToDashboard() {\n      this.router.navigate(['/dashboard']);\n    }\n    goToProfile() {\n      this.router.navigate(['/profile']);\n    }\n    goToLogin() {\n      this.router.navigate(['/auth/login']);\n    }\n    requestNewLink() {\n      this.router.navigate(['/auth/login'], {\n        queryParams: {\n          message: 'Please use the \"Request disable 2FA\" option to get a new link.'\n        }\n      });\n    }\n    static #_ = this.ɵfac = function Disable2FAConfirmComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Disable2FAConfirmComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.TwoFactorService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: Disable2FAConfirmComponent,\n      selectors: [[\"app-disable-2fa-confirm\"]],\n      standalone: false,\n      decls: 5,\n      vars: 3,\n      consts: [[1, \"disable-confirm-container\"], [1, \"disable-confirm-card\"], [\"class\", \"loading-state\", 4, \"ngIf\"], [\"class\", \"success-state\", 4, \"ngIf\"], [\"class\", \"error-state\", 4, \"ngIf\"], [1, \"loading-state\"], [\"diameter\", \"60\"], [1, \"success-state\"], [1, \"success-icon\"], [1, \"success-message\"], [\"class\", \"user-info\", 4, \"ngIf\"], [1, \"security-notice\"], [1, \"warning-icon\"], [1, \"notice-content\"], [1, \"action-buttons\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-button\", \"\", 3, \"click\"], [1, \"user-info\"], [1, \"error-state\"], [1, \"error-icon\"], [1, \"error-message\"], [1, \"error-help\"]],\n      template: function Disable2FAConfirmComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1);\n          i0.ɵɵtemplate(2, Disable2FAConfirmComponent_div_2_Template, 6, 0, \"div\", 2)(3, Disable2FAConfirmComponent_div_3_Template, 32, 1, \"div\", 3)(4, Disable2FAConfirmComponent_div_4_Template, 26, 1, \"div\", 4);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.success);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.success);\n        }\n      },\n      dependencies: [i5.NgIf, i6.MatCard, i7.MatButton, i8.MatIcon, i9.MatProgressSpinner],\n      styles: [\".disable-confirm-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:100vh;background:linear-gradient(135deg,#667eea,#764ba2);padding:20px}.disable-confirm-card[_ngcontent-%COMP%]{max-width:600px;width:100%;padding:40px;text-align:center;box-shadow:0 10px 30px #0000004d}.loading-state[_ngcontent-%COMP%]{padding:20px}.loading-state[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:20px 0 10px;color:#333}.loading-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#666;margin-bottom:0}.success-state[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%]{padding:20px}.success-icon[_ngcontent-%COMP%]{font-size:80px;width:80px;height:80px;color:#4caf50;margin-bottom:20px}.error-icon[_ngcontent-%COMP%]{font-size:80px;width:80px;height:80px;color:#f44336;margin-bottom:20px}.success-state[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .error-state[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0 0 20px;color:#333}.success-message[_ngcontent-%COMP%], .error-message[_ngcontent-%COMP%]{font-size:16px;color:#666;margin-bottom:30px;line-height:1.6}.user-info[_ngcontent-%COMP%]{background:#f8f9fa;border-radius:8px;padding:20px;margin:20px 0;text-align:left}.user-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0 0 15px;color:#333;font-size:16px}.user-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:8px 0;color:#555}.security-notice[_ngcontent-%COMP%]{background:#fff3e0;border:1px solid #ff9800;border-radius:8px;padding:20px;margin:20px 0;display:flex;align-items:flex-start;gap:16px;text-align:left}.warning-icon[_ngcontent-%COMP%]{color:#ff9800;font-size:32px;width:32px;height:32px;flex-shrink:0}.notice-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 10px;color:#f57c00;font-size:16px}.notice-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 10px;color:#e65100}.notice-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:0;padding-left:20px;color:#e65100}.notice-content[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:5px}.error-help[_ngcontent-%COMP%]{background:#ffebee;border:1px solid #f44336;border-radius:8px;padding:20px;margin:20px 0;text-align:left}.error-help[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 10px;color:#d32f2f;font-size:16px}.error-help[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{margin:0;padding-left:20px;color:#c62828}.error-help[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin-bottom:8px}.action-buttons[_ngcontent-%COMP%]{margin-top:30px;display:flex;gap:16px;justify-content:center;flex-wrap:wrap}.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{min-width:150px}@media (max-width: 600px){.disable-confirm-container[_ngcontent-%COMP%]{padding:10px}.disable-confirm-card[_ngcontent-%COMP%]{padding:20px}.action-buttons[_ngcontent-%COMP%]{flex-direction:column;align-items:center}.action-buttons[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:100%;max-width:250px}}\"]\n    });\n  }\n  return Disable2FAConfirmComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}