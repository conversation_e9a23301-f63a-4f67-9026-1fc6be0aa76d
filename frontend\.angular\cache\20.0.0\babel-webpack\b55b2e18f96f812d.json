{"ast": null, "code": "/**\n * @license Angular v20.0.0\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { SIGNAL, producerUpdateValueVersion, signalSetFn, producerMarkClean, signalUpdateFn, producerAccessed, ERRORED, defaultEquals, UNSET, REACTIVE_NODE, COMPUTING, consumerBeforeComputation, consumerAfterComputation, runPostProducerCreatedFn, setActiveConsumer } from './signal-ePSl6jXn.mjs';\nfunction createLinkedSignal(sourceFn, computationFn, equalityFn) {\n  const node = Object.create(LINKED_SIGNAL_NODE);\n  node.source = sourceFn;\n  node.computation = computationFn;\n  if (equalityFn != undefined) {\n    node.equal = equalityFn;\n  }\n  const linkedSignalGetter = () => {\n    // Check if the value needs updating before returning it.\n    producerUpdateValueVersion(node);\n    // Record that someone looked at this signal.\n    producerAccessed(node);\n    if (node.value === ERRORED) {\n      throw node.error;\n    }\n    return node.value;\n  };\n  const getter = linkedSignalGetter;\n  getter[SIGNAL] = node;\n  if (typeof ngDevMode !== 'undefined' && ngDevMode) {\n    const debugName = node.debugName ? ' (' + node.debugName + ')' : '';\n    getter.toString = () => `[LinkedSignal${debugName}: ${node.value}]`;\n  }\n  runPostProducerCreatedFn(node);\n  return getter;\n}\nfunction linkedSignalSetFn(node, newValue) {\n  producerUpdateValueVersion(node);\n  signalSetFn(node, newValue);\n  producerMarkClean(node);\n}\nfunction linkedSignalUpdateFn(node, updater) {\n  producerUpdateValueVersion(node);\n  signalUpdateFn(node, updater);\n  producerMarkClean(node);\n}\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `LINKED_SIGNAL_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst LINKED_SIGNAL_NODE = /* @__PURE__ */(() => {\n  return {\n    ...REACTIVE_NODE,\n    value: UNSET,\n    dirty: true,\n    error: null,\n    equal: defaultEquals,\n    kind: 'linkedSignal',\n    producerMustRecompute(node) {\n      // Force a recomputation if there's no current value, or if the current value is in the\n      // process of being calculated (which should throw an error).\n      return node.value === UNSET || node.value === COMPUTING;\n    },\n    producerRecomputeValue(node) {\n      if (node.value === COMPUTING) {\n        // Our computation somehow led to a cyclic read of itself.\n        throw new Error(typeof ngDevMode !== 'undefined' && ngDevMode ? 'Detected cycle in computations.' : '');\n      }\n      const oldValue = node.value;\n      node.value = COMPUTING;\n      const prevConsumer = consumerBeforeComputation(node);\n      let newValue;\n      try {\n        const newSourceValue = node.source();\n        const prev = oldValue === UNSET || oldValue === ERRORED ? undefined : {\n          source: node.sourceValue,\n          value: oldValue\n        };\n        newValue = node.computation(newSourceValue, prev);\n        node.sourceValue = newSourceValue;\n      } catch (err) {\n        newValue = ERRORED;\n        node.error = err;\n      } finally {\n        consumerAfterComputation(node, prevConsumer);\n      }\n      if (oldValue !== UNSET && newValue !== ERRORED && node.equal(oldValue, newValue)) {\n        // No change to `valueVersion` - old and new values are\n        // semantically equivalent.\n        node.value = oldValue;\n        return;\n      }\n      node.value = newValue;\n      node.version++;\n    }\n  };\n})();\n\n/**\n * Execute an arbitrary function in a non-reactive (non-tracking) context. The executed function\n * can, optionally, return a value.\n */\nfunction untracked(nonReactiveReadsFn) {\n  const prevConsumer = setActiveConsumer(null);\n  // We are not trying to catch any particular errors here, just making sure that the consumers\n  // stack is restored in case of errors.\n  try {\n    return nonReactiveReadsFn();\n  } finally {\n    setActiveConsumer(prevConsumer);\n  }\n}\nexport { createLinkedSignal, linkedSignalSetFn, linkedSignalUpdateFn, untracked };\n//# sourceMappingURL=untracked-2ouAFbCz.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}